{"name": "inventory-sales-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tanstack/react-query": "^5.17.0", "axios": "^1.6.5", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "lucide-react": "^0.344.0", "puppeteer": "^24.14.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.21.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/file-saver": "^2.0.7", "@types/node": "^24.0.12", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}