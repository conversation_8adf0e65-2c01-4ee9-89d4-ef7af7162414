#!/usr/bin/env node
/**
 * Frontend Test Runner
 * Runs the diamond frontend tests using Node.js
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

function checkNodeModules() {
    const puppeteerPath = path.join(__dirname, 'node_modules', 'puppeteer');
    if (!fs.existsSync(puppeteerPath)) {
        console.log('❌ Puppeteer not installed');
        console.log('💡 Install with: npm install puppeteer');
        return false;
    }
    return true;
}

function runFrontendTests() {
    console.log('🧪 FRONTEND DIAMOND SYSTEM TESTING');
    console.log('='.repeat(50));
    
    if (!checkNodeModules()) {
        console.log('\n📦 INSTALLING PUPPETEER...');
        const install = spawn('npm', ['install', 'puppeteer'], { stdio: 'inherit' });
        
        install.on('close', (code) => {
            if (code === 0) {
                console.log('✅ Puppeteer installed successfully');
                runTests();
            } else {
                console.log('❌ Failed to install Puppeteer');
                console.log('💡 Try running: npm install puppeteer');
            }
        });
    } else {
        runTests();
    }
}

function runTests() {
    console.log('\n🚀 STARTING FRONTEND TESTS...');
    
    // Check if frontend server is running
    const http = require('http');
    const options = {
        hostname: 'localhost',
        port: 5173,
        path: '/',
        method: 'GET',
        timeout: 5000
    };
    
    const req = http.request(options, (res) => {
        if (res.statusCode === 200) {
            console.log('✅ Frontend server is running on http://localhost:5173');
            executeTests();
        } else {
            console.log(`⚠️  Frontend server responded with status: ${res.statusCode}`);
            executeTests();
        }
    });
    
    req.on('error', (err) => {
        console.log('❌ Frontend server is not running');
        console.log('💡 Please start the frontend server with: npm run dev');
        console.log('💡 Then run this test again');
        process.exit(1);
    });
    
    req.on('timeout', () => {
        console.log('❌ Frontend server connection timeout');
        console.log('💡 Please ensure frontend is running on http://localhost:5173');
        process.exit(1);
    });
    
    req.end();
}

function executeTests() {
    console.log('\n🔍 EXECUTING FRONTEND TESTS...');
    
    const testFile = path.join(__dirname, 'test_diamond_frontend_comprehensive.js');
    
    if (!fs.existsSync(testFile)) {
        console.log('❌ Test file not found: test_diamond_frontend_comprehensive.js');
        process.exit(1);
    }
    
    const node = spawn('node', [testFile], { stdio: 'inherit' });
    
    node.on('close', (code) => {
        console.log(`\n📊 Frontend tests completed with exit code: ${code}`);
        if (code === 0) {
            console.log('🎉 All frontend tests passed!');
        } else {
            console.log('❌ Some frontend tests failed');
        }
    });
    
    node.on('error', (err) => {
        console.log('❌ Error running frontend tests:', err.message);
        console.log('💡 Make sure Node.js is installed and accessible');
    });
}

// Simple alternative test without Puppeteer
function runSimpleTests() {
    console.log('\n🔍 RUNNING SIMPLE FRONTEND TESTS...');
    console.log('(Without browser automation)');
    
    const http = require('http');
    
    const pages = [
        '/',
        '/diamonds',
        '/jewelry', 
        '/sales',
        '/manufacturing'
    ];
    
    let testCount = 0;
    let passedCount = 0;
    
    function testPage(page) {
        return new Promise((resolve) => {
            const options = {
                hostname: 'localhost',
                port: 5173,
                path: page,
                method: 'GET',
                timeout: 5000
            };
            
            const req = http.request(options, (res) => {
                testCount++;
                if (res.statusCode === 200) {
                    console.log(`✅ ${page}: Accessible`);
                    passedCount++;
                } else {
                    console.log(`❌ ${page}: Status ${res.statusCode}`);
                }
                resolve();
            });
            
            req.on('error', () => {
                testCount++;
                console.log(`❌ ${page}: Connection error`);
                resolve();
            });
            
            req.on('timeout', () => {
                testCount++;
                console.log(`❌ ${page}: Timeout`);
                resolve();
            });
            
            req.end();
        });
    }
    
    Promise.all(pages.map(testPage)).then(() => {
        console.log('\n📊 SIMPLE TEST SUMMARY');
        console.log('='.repeat(30));
        console.log(`Total Tests: ${testCount}`);
        console.log(`Passed: ${passedCount}`);
        console.log(`Failed: ${testCount - passedCount}`);
        console.log(`Success Rate: ${((passedCount/testCount)*100).toFixed(1)}%`);
        
        if (passedCount === testCount) {
            console.log('\n🎉 All pages are accessible!');
        } else {
            console.log('\n⚠️  Some pages are not accessible');
            console.log('💡 Check if the frontend server is running properly');
        }
    });
}

// Main execution
if (process.argv.includes('--simple')) {
    console.log('🧪 SIMPLE FRONTEND ACCESSIBILITY TEST');
    console.log('='.repeat(50));
    runSimpleTests();
} else {
    runFrontendTests();
}

// Help message
if (process.argv.includes('--help')) {
    console.log('\n📖 USAGE:');
    console.log('node run_frontend_tests.js          # Run full tests with Puppeteer');
    console.log('node run_frontend_tests.js --simple # Run simple accessibility tests');
    console.log('node run_frontend_tests.js --help   # Show this help message');
}
