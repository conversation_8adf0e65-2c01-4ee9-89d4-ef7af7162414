#!/usr/bin/env python3
"""
Simple Backend Connection Test
Tests if the backend is running and accessible
"""

import requests
import json

def test_backend_connection():
    base_url = 'http://localhost:8000'
    api_url = f'{base_url}/api'
    
    print("🔍 TESTING BACKEND CONNECTION")
    print("=" * 50)
    
    # Test 1: Check if server is running
    try:
        response = requests.get(base_url, timeout=5)
        print(f"✅ Server is running: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        print("💡 Please start the backend server with: python run.py")
        return False
    except Exception as e:
        print(f"❌ Error connecting to server: {e}")
        return False
    
    # Test 2: Check API documentation
    try:
        response = requests.get(f"{api_url}/docs", timeout=5)
        if response.status_code == 200:
            print(f"✅ API documentation accessible: {api_url}/docs")
        else:
            print(f"⚠️  API docs status: {response.status_code}")
    except Exception as e:
        print(f"⚠️  API docs error: {e}")
    
    # Test 3: Try authentication
    print("\n🔐 TESTING AUTHENTICATION")
    print("-" * 30)
    
    auth_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{api_url}/auth/login", json=auth_data, timeout=5)
        if response.status_code == 200:
            data = response.json()
            if 'access_token' in data:
                print("✅ Authentication successful")
                token = data['access_token']
                
                # Test 4: Test authenticated endpoints
                print("\n📊 TESTING DASHBOARD ENDPOINTS")
                print("-" * 30)
                
                headers = {'Authorization': f'Bearer {token}'}
                
                endpoints = [
                    '/dashboard/summary',
                    '/dashboard/activity', 
                    '/dashboard/analytics',
                    '/dashboard/alerts',
                    '/dashboard/sales',
                    '/dashboard/stock'
                ]
                
                for endpoint in endpoints:
                    try:
                        response = requests.get(f"{api_url}{endpoint}", headers=headers, timeout=5)
                        if response.status_code == 200:
                            print(f"✅ {endpoint}: Working")
                        elif response.status_code == 404:
                            print(f"❌ {endpoint}: Not found (404)")
                        elif response.status_code == 401:
                            print(f"❌ {endpoint}: Unauthorized (401)")
                        else:
                            print(f"⚠️  {endpoint}: Status {response.status_code}")
                    except Exception as e:
                        print(f"❌ {endpoint}: Error - {e}")
                
                return True
            else:
                print("❌ Authentication failed: No access token in response")
                return False
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            if response.status_code == 401:
                print("💡 Check if admin user exists with password 'admin123'")
            return False
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False

def test_diamond_endpoints():
    """Test diamond-related endpoints"""
    base_url = 'http://localhost:8000/api'
    
    print("\n💎 TESTING DIAMOND ENDPOINTS")
    print("-" * 30)
    
    # Try to authenticate first
    auth_data = {"username": "admin", "password": "admin123"}
    
    try:
        response = requests.post(f"{base_url}/auth/login", json=auth_data, timeout=5)
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            headers = {'Authorization': f'Bearer {token}'}
            
            # Test diamond endpoints
            diamond_endpoints = [
                '/diamonds',
                '/jewelry', 
                '/sales',
                '/manufacturing'
            ]
            
            for endpoint in diamond_endpoints:
                try:
                    response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        count = len(data) if isinstance(data, list) else 'N/A'
                        print(f"✅ {endpoint}: Working ({count} items)")
                    else:
                        print(f"⚠️  {endpoint}: Status {response.status_code}")
                except Exception as e:
                    print(f"❌ {endpoint}: Error - {e}")
        else:
            print("❌ Could not authenticate for diamond endpoint tests")
    except Exception as e:
        print(f"❌ Diamond endpoint test error: {e}")

if __name__ == "__main__":
    print("🧪 BACKEND CONNECTION & ENDPOINT TEST")
    print("=" * 50)
    
    backend_ok = test_backend_connection()
    
    if backend_ok:
        test_diamond_endpoints()
        print("\n🎉 BACKEND CONNECTION TEST COMPLETE")
        print("💡 If any endpoints show 404, try restarting the backend server")
    else:
        print("\n❌ BACKEND CONNECTION FAILED")
        print("💡 Please ensure the backend server is running on http://localhost:8000")
    
    print("\n📝 NEXT STEPS:")
    print("1. Ensure backend server is running: python run.py")
    print("2. Check admin user credentials (admin/admin123)")
    print("3. Restart server if endpoints show 404 errors")
    print("4. Run tests again: python test_diamond_system_comprehensive.py")
