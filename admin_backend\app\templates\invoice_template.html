<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice #{{ sale.invoice_no }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 30px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
        }

        .company-info {
            flex: 1;
        }

        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }

        .company-details {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        .invoice-info {
            text-align: right;
            flex: 1;
        }

        .invoice-title {
            font-size: 32px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }

        .invoice-number {
            font-size: 18px;
            color: #2563eb;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .invoice-date {
            font-size: 14px;
            color: #666;
        }

        .billing-section {
            display: flex;
            justify-content: space-between;
            margin: 30px 0;
        }

        .billing-info {
            flex: 1;
            margin-right: 20px;
        }

        .billing-title {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .billing-details {
            font-size: 14px;
            line-height: 1.6;
        }

        .payment-info {
            flex: 1;
            text-align: right;
        }

        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-paid {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-unpaid {
            background-color: #fef3c7;
            color: #92400e;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .items-table th {
            background-color: #f8fafc;
            color: #374151;
            font-weight: 600;
            padding: 15px 12px;
            text-align: left;
            border-bottom: 2px solid #e5e7eb;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .items-table td {
            padding: 15px 12px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 14px;
        }

        .items-table tr:hover {
            background-color: #f9fafb;
        }

        .item-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .item-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .item-details {
            font-size: 12px;
            color: #6b7280;
        }

        .totals-section {
            margin-top: 30px;
            display: flex;
            justify-content: flex-end;
        }

        .totals-table {
            width: 300px;
        }

        .totals-table td {
            padding: 8px 12px;
            border: none;
            font-size: 14px;
        }

        .totals-table .label {
            text-align: right;
            font-weight: 600;
            color: #374151;
        }

        .totals-table .amount {
            text-align: right;
            font-weight: 600;
            width: 120px;
        }

        .total-row {
            border-top: 2px solid #2563eb;
            background-color: #f8fafc;
        }

        .total-row td {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            padding: 15px 12px;
        }

        .notes-section {
            margin-top: 40px;
            padding: 20px;
            background-color: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #2563eb;
        }

        .notes-title {
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .notes-content {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.6;
        }

        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            font-size: 12px;
            color: #9ca3af;
        }

        .footer-company {
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 5px;
        }

        @media print {
            .container {
                padding: 20px;
            }

            .header {
                margin-bottom: 30px;
            }

            .company-name {
                font-size: 24px;
            }

            .invoice-title {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div class="company-info">
                <div class="company-name">Diamond Jewelry Store</div>
                <div class="company-details">
                    123 Jewelry Street<br>
                    Diamond District, City 12345<br>
                    Phone: (*************<br>
                    Email: <EMAIL><br>
                    Website: www.diamondjewelry.com
                </div>
            </div>
            <div class="invoice-info">
                <div class="invoice-title">INVOICE</div>
                <div class="invoice-number">#{{ sale.invoice_no }}</div>
                <div class="invoice-date">Date: {{ sale.sale_date.strftime('%B %d, %Y') if sale.sale_date else 'N/A' }}</div>
                {% if sale.order_number %}
                <div class="invoice-date">Order: {{ sale.order_number }}</div>
                {% endif %}
            </div>
        </div>

        <!-- Billing Information -->
        <div class="billing-section">
            <div class="billing-info">
                <div class="billing-title">Bill To:</div>
                <div class="billing-details">
                    <strong>{{ sale.customer_name }}</strong><br>
                    {% if sale.customer_email %}{{ sale.customer_email }}<br>{% endif %}
                    {% if sale.customer_phone %}{{ sale.customer_phone }}<br>{% endif %}
                    {% if sale.customer_address %}
                        {{ sale.customer_address }}<br>
                        {% if sale.customer_city %}{{ sale.customer_city }}<br>{% endif %}
                        {% if sale.customer_state %}{{ sale.customer_state }} {% endif %}
                        {% if sale.customer_zip %}{{ sale.customer_zip }}{% endif %}
                    {% endif %}
                </div>
            </div>
            <div class="payment-info">
                <div class="billing-title">Payment Status:</div>
                <span class="status-badge {% if sale.payment_status == 'paid' %}status-paid{% else %}status-unpaid{% endif %}">
                    {{ sale.payment_status.title() }}
                </span>
                {% if sale.payment_method %}
                <div style="margin-top: 10px; font-size: 14px;">
                    <strong>Method:</strong> {{ sale.payment_method.title() }}
                </div>
                {% endif %}
                {% if sale.payment_date %}
                <div style="margin-top: 5px; font-size: 14px;">
                    <strong>Paid:</strong> {{ sale.payment_date.strftime('%B %d, %Y') }}
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 60px;">Image</th>
                    <th>Item Details</th>
                    <th style="width: 120px;">Design Code</th>
                    <th style="width: 100px;">Weight</th>
                    <th style="width: 100px;">Metal Type</th>
                    <th style="width: 80px;">Qty</th>
                    <th style="width: 120px; text-align: right;">Unit Price</th>
                    <th style="width: 120px; text-align: right;">Total</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        {% if jewelry and jewelry.image_path %}
                            <img src="{{ jewelry.image_path }}" alt="{{ jewelry.name }}" class="item-image">
                        {% else %}
                            <div style="width: 60px; height: 60px; background-color: #f3f4f6; border-radius: 6px; display: flex; align-items: center; justify-content: center; font-size: 10px; color: #9ca3af;">No Image</div>
                        {% endif %}
                    </td>
                    <td>
                        <div class="item-name">{{ jewelry.name if jewelry else 'Jewelry Item' }}</div>
                        <div class="item-details">
                            {% if jewelry %}
                                {% if jewelry.category %}Category: {{ jewelry.category }}<br>{% endif %}
                                {% if jewelry.subcategory %}Type: {{ jewelry.subcategory }}<br>{% endif %}
                                {% if jewelry.description %}{{ jewelry.description[:100] }}{% if jewelry.description|length > 100 %}...{% endif %}{% endif %}
                            {% endif %}
                        </div>
                    </td>
                    <td>{{ jewelry.design_code if jewelry else 'N/A' }}</td>
                    <td>
                        {% if jewelry %}
                            {% if jewelry.gross_weight %}{{ jewelry.gross_weight }}g{% endif %}
                            {% if jewelry.net_weight %}<br><small>Net: {{ jewelry.net_weight }}g</small>{% endif %}
                        {% else %}
                            N/A
                        {% endif %}
                    </td>
                    <td>{{ jewelry.metal_type if jewelry else 'N/A' }}</td>
                    <td style="text-align: center;">1</td>
                    <td style="text-align: right;">₹{{ "{:,.2f}".format(sale.total_amount) }}</td>
                    <td style="text-align: right;">₹{{ "{:,.2f}".format(sale.total_amount) }}</td>
                </tr>
            </tbody>
        </table>

        <!-- Totals Section -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td class="label">Subtotal:</td>
                    <td class="amount">₹{{ "{:,.2f}".format(sale.total_amount) }}</td>
                </tr>
                {% if sale.discount_amount and sale.discount_amount > 0 %}
                <tr>
                    <td class="label">Discount:</td>
                    <td class="amount">-₹{{ "{:,.2f}".format(sale.discount_amount) }}</td>
                </tr>
                {% endif %}
                {% if sale.tax_amount and sale.tax_amount > 0 %}
                <tr>
                    <td class="label">Tax:</td>
                    <td class="amount">₹{{ "{:,.2f}".format(sale.tax_amount) }}</td>
                </tr>
                {% endif %}
                <tr class="total-row">
                    <td class="label">Total Amount:</td>
                    <td class="amount">₹{{ "{:,.2f}".format(sale.total_amount) }}</td>
                </tr>
            </table>
        </div>

        <!-- Notes Section -->
        {% if sale.notes %}
        <div class="notes-section">
            <div class="notes-title">Notes:</div>
            <div class="notes-content">{{ sale.notes }}</div>
        </div>
        {% endif %}

        <!-- Footer -->
        <div class="footer">
            <div class="footer-company">Diamond Jewelry Store</div>
            <div>Thank you for choosing us for your precious jewelry needs!</div>
            <div>For any queries, please contact <NAME_EMAIL> or (*************</div>
        </div>
    </div>
</body>
</html>
