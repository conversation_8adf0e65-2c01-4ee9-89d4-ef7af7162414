#!/usr/bin/env python3
"""
Diamond Data Diagnostic Script
Checks the actual diamond data in the database to identify why carat weight is 0
"""

import sys
import os
import requests
import json

def diagnose_diamond_data():
    """Diagnose diamond data issues"""
    base_url = 'http://localhost:8000/api'
    
    print("🔍 DIAMOND DATA DIAGNOSTIC")
    print("=" * 50)
    
    # Try to authenticate
    try:
        auth_data = {"username": "admin", "password": "admin123"}
        response = requests.post(f"{base_url}/auth/login", json=auth_data, timeout=5)
        
        if response.status_code != 200:
            print("❌ Authentication failed")
            print("💡 Check if backend is running and admin credentials are correct")
            return False
        
        data = response.json()
        token = data.get('access_token')
        headers = {'Authorization': f'Bearer {token}'}
        
        print("✅ Authentication successful")
        
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False
    
    # Get diamond data
    try:
        print("\n📊 FETCHING DIAMOND DATA")
        print("-" * 30)
        
        response = requests.get(f"{base_url}/diamonds", headers=headers, timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to fetch diamonds: {response.status_code}")
            return False
        
        diamonds = response.json()
        print(f"✅ Found {len(diamonds)} diamonds")
        
        if len(diamonds) == 0:
            print("⚠️  No diamonds found in database")
            print("💡 Add some diamonds to test the carat weight calculation")
            return False
        
        # Analyze diamond data
        print("\n🔍 ANALYZING DIAMOND DATA")
        print("-" * 30)
        
        total_diamonds = len(diamonds)
        diamonds_with_carat = 0
        diamonds_with_quantity = 0
        diamonds_with_available_quantity = 0
        total_carat = 0
        total_quantity = 0
        total_available_quantity = 0
        
        print("First 5 diamonds:")
        for i, diamond in enumerate(diamonds[:5]):
            print(f"\nDiamond {i+1}:")
            print(f"  ID: {diamond.get('id')}")
            print(f"  Carat: {diamond.get('carat')}")
            print(f"  Quantity: {diamond.get('quantity')}")
            print(f"  Available Quantity: {diamond.get('available_quantity')}")
            print(f"  Reserved Quantity: {diamond.get('reserved_quantity')}")
            print(f"  Status: {diamond.get('status')}")
            print(f"  Shape: {diamond.get('shape')}")
            print(f"  Color: {diamond.get('color')}")
            print(f"  Clarity: {diamond.get('clarity')}")
        
        # Count diamonds with valid data
        for diamond in diamonds:
            carat = diamond.get('carat')
            quantity = diamond.get('quantity')
            available_quantity = diamond.get('available_quantity')
            
            if carat is not None and carat > 0:
                diamonds_with_carat += 1
                total_carat += float(carat)
            
            if quantity is not None and quantity > 0:
                diamonds_with_quantity += 1
                total_quantity += int(quantity)
            
            if available_quantity is not None and available_quantity > 0:
                diamonds_with_available_quantity += 1
                total_available_quantity += int(available_quantity)
        
        print(f"\n📈 SUMMARY STATISTICS")
        print("-" * 30)
        print(f"Total diamonds: {total_diamonds}")
        print(f"Diamonds with carat > 0: {diamonds_with_carat}")
        print(f"Diamonds with quantity > 0: {diamonds_with_quantity}")
        print(f"Diamonds with available_quantity > 0: {diamonds_with_available_quantity}")
        print(f"Total carat weight: {total_carat:.3f}ct")
        print(f"Total quantity: {total_quantity}")
        print(f"Total available quantity: {total_available_quantity}")
        
        if diamonds_with_carat > 0:
            avg_carat = total_carat / diamonds_with_carat
            print(f"Average carat per diamond: {avg_carat:.3f}ct")
        
        # Identify issues
        print(f"\n🚨 ISSUES IDENTIFIED")
        print("-" * 30)
        
        issues = []
        
        if diamonds_with_carat == 0:
            issues.append("❌ No diamonds have carat values > 0")
        elif diamonds_with_carat < total_diamonds:
            issues.append(f"⚠️  Only {diamonds_with_carat}/{total_diamonds} diamonds have carat values")
        
        if diamonds_with_quantity == 0:
            issues.append("❌ No diamonds have quantity values > 0")
        elif diamonds_with_quantity < total_diamonds:
            issues.append(f"⚠️  Only {diamonds_with_quantity}/{total_diamonds} diamonds have quantity values")
        
        if diamonds_with_available_quantity == 0:
            issues.append("❌ No diamonds have available_quantity values > 0")
        elif diamonds_with_available_quantity < total_diamonds:
            issues.append(f"⚠️  Only {diamonds_with_available_quantity}/{total_diamonds} diamonds have available_quantity values")
        
        if not issues:
            issues.append("✅ All diamonds have valid carat, quantity, and available_quantity values")
        
        for issue in issues:
            print(issue)
        
        # Test dashboard calculation
        print(f"\n🧮 MANUAL CALCULATION TEST")
        print("-" * 30)
        
        manual_total_carat = sum(
            float(d.get('carat', 0)) * int(d.get('quantity', 0))
            for d in diamonds
            if d.get('carat') and d.get('quantity')
        )
        
        manual_available_carat = sum(
            float(d.get('carat', 0)) * int(d.get('available_quantity', 0))
            for d in diamonds
            if d.get('carat') and d.get('available_quantity') and d.get('status') in ['in_stock', 'reserved']
        )
        
        print(f"Manual total carat calculation: {manual_total_carat:.3f}ct")
        print(f"Manual available carat calculation: {manual_available_carat:.3f}ct")
        
        # Get dashboard data for comparison
        try:
            response = requests.get(f"{base_url}/dashboard/summary", headers=headers, timeout=10)
            if response.status_code == 200:
                dashboard_data = response.json()
                print(f"\n📊 DASHBOARD API RESULTS")
                print("-" * 30)
                print(f"API total_carat_weight: {dashboard_data.get('total_carat_weight', 'N/A')}")
                print(f"API available_carat_weight: {dashboard_data.get('available_carat_weight', 'N/A')}")
                print(f"API diamonds_in_stock: {dashboard_data.get('diamonds_in_stock', 'N/A')}")
                print(f"API total_diamonds: {dashboard_data.get('total_diamonds', 'N/A')}")
                
                # Compare results
                api_total_carat = dashboard_data.get('total_carat_weight', 0)
                api_available_carat = dashboard_data.get('available_carat_weight', 0)
                
                if abs(manual_total_carat - api_total_carat) > 0.001:
                    print(f"⚠️  Mismatch in total carat: Manual={manual_total_carat:.3f}, API={api_total_carat}")
                else:
                    print(f"✅ Total carat calculation matches")
                
                if abs(manual_available_carat - api_available_carat) > 0.001:
                    print(f"⚠️  Mismatch in available carat: Manual={manual_available_carat:.3f}, API={api_available_carat}")
                else:
                    print(f"✅ Available carat calculation matches")
            else:
                print(f"❌ Failed to get dashboard data: {response.status_code}")
        except Exception as e:
            print(f"❌ Error getting dashboard data: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing diamond data: {e}")
        return False

def suggest_fixes():
    """Suggest fixes based on the diagnosis"""
    print(f"\n💡 SUGGESTED FIXES")
    print("-" * 30)
    print("1. If diamonds have NULL carat values:")
    print("   - Update diamonds to have valid carat values")
    print("   - Check diamond creation forms to ensure carat is required")
    
    print("\n2. If diamonds have NULL quantity values:")
    print("   - Update diamonds to have valid quantity values (default: 1)")
    print("   - Check diamond creation logic")
    
    print("\n3. If available_quantity is not calculated:")
    print("   - Run: UPDATE diamonds SET available_quantity = quantity - COALESCE(reserved_quantity, 0)")
    print("   - Check Diamond model's update_available_quantity() method")
    
    print("\n4. If backend calculation is wrong:")
    print("   - Check SQL query in dashboard.py")
    print("   - Restart backend server")
    print("   - Check database connection")

if __name__ == "__main__":
    print("🧪 DIAMOND DATA DIAGNOSTIC TOOL")
    print("=" * 50)
    
    success = diagnose_diamond_data()
    
    if success:
        suggest_fixes()
    
    print("\n📝 NEXT STEPS:")
    print("1. Fix any identified data issues")
    print("2. Restart backend server")
    print("3. Refresh dashboard to see updated carat weights")
    print("4. Run comprehensive tests again")
