{"data_mtime": 1753271372, "dep_lines": [2, 3, 4, 5, 6, 386, 7, 8, 475, 384, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 5, 5, 20, 20, 5, 30, 30, 30, 30, 5], "dependencies": ["app.models.diamond", "app.models.jewelry", "app.models.sale", "app.models.manufacturing", "app.utils.decorators", "app.models.vendor", "app", "sqlalchemy", "logging", "datetime", "builtins", "_frozen_importlib", "abc", "app.utils", "typing"], "hash": "4e8b42ce200d67bc107414c0696d825bb858586c", "id": "app.api.dashboard", "ignore_all": true, "interface_hash": "f3d4563777c1b61d6e2e991c8bfca652da977212", "mtime": 1753270554, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\dashboard.py", "plugin_data": null, "size": 26065, "suppressed": ["flask_restx"], "version_id": "1.15.0"}