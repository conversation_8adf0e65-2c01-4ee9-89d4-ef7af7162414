/**
 * Comprehensive Diamond Frontend Testing
 * Tests all frontend diamond functionality for production readiness
 */

const puppeteer = require('puppeteer');

class DiamondFrontendTester {
    constructor(baseUrl = 'http://localhost:5173') {
        this.baseUrl = baseUrl;
        this.browser = null;
        this.page = null;
        this.testResults = [];
    }

    async logTest(testName, success, message = "", data = null) {
        const result = {
            test: testName,
            success,
            message,
            timestamp: new Date().toISOString(),
            data
        };
        this.testResults.push(result);
        const status = success ? "✅ PASS" : "❌ FAIL";
        console.log(`${status} ${testName}: ${message}`);
    }

    async setup() {
        try {
            this.browser = await puppeteer.launch({
                headless: false, // Set to true for CI/CD
                defaultViewport: { width: 1280, height: 720 }
            });
            this.page = await this.browser.newPage();
            
            // Set up console logging
            this.page.on('console', msg => {
                if (msg.type() === 'error') {
                    console.log('Browser Error:', msg.text());
                }
            });

            await this.logTest("Setup", true, "Browser initialized");
            return true;
        } catch (error) {
            await this.logTest("Setup", false, `Setup failed: ${error.message}`);
            return false;
        }
    }

    async testDashboardDiamondDisplay() {
        try {
            await this.page.goto(`${this.baseUrl}/`);
            await this.page.waitForSelector('[data-testid="dashboard"], .dashboard, h1', { timeout: 10000 });

            // Check for diamond statistics cards
            const diamondCards = await this.page.$$eval('[class*="card"], .card', cards => {
                return cards.filter(card => {
                    const text = card.textContent.toLowerCase();
                    return text.includes('diamond') || text.includes('available') || text.includes('reserved');
                }).length;
            });

            if (diamondCards === 0) {
                await this.logTest("Dashboard Diamond Display", false, "No diamond statistics cards found");
                return false;
            }

            // Check for stock summary section
            const stockSummary = await this.page.$eval('body', body => {
                const text = body.textContent.toLowerCase();
                return text.includes('stock summary') || text.includes('diamonds available');
            });

            if (!stockSummary) {
                await this.logTest("Dashboard Diamond Display", false, "Stock summary section not found");
                return false;
            }

            // Check for analytics charts
            const hasCharts = await this.page.$eval('body', body => {
                const text = body.textContent.toLowerCase();
                return text.includes('diamond status distribution') || text.includes('shape distribution');
            });

            await this.logTest("Dashboard Diamond Display", true, 
                `Dashboard displays diamond data correctly. Cards: ${diamondCards}, Charts: ${hasCharts}`);
            return true;

        } catch (error) {
            await this.logTest("Dashboard Diamond Display", false, `Error: ${error.message}`);
            return false;
        }
    }

    async testDiamondListPage() {
        try {
            await this.page.goto(`${this.baseUrl}/diamonds`);
            await this.page.waitForSelector('table, [class*="grid"], .diamond-list', { timeout: 10000 });

            // Check for diamond table/grid
            const hasDiamondList = await this.page.$('table tbody tr, [class*="diamond-card"], .diamond-item');
            if (!hasDiamondList) {
                await this.logTest("Diamond List Page", false, "No diamond list found");
                return false;
            }

            // Check for search functionality
            const searchInput = await this.page.$('input[type="search"], input[placeholder*="search"]');
            if (!searchInput) {
                await this.logTest("Diamond List Page", false, "Search input not found");
                return false;
            }

            // Check for filters
            const filters = await this.page.$$('select, [class*="filter"]');
            if (filters.length < 3) {
                await this.logTest("Diamond List Page", false, `Insufficient filters found: ${filters.length}`);
                return false;
            }

            // Check for add diamond button
            const addButton = await this.page.$('button:has-text("Add"), button[class*="add"]');
            if (!addButton) {
                await this.logTest("Diamond List Page", false, "Add diamond button not found");
                return false;
            }

            await this.logTest("Diamond List Page", true, "Diamond list page has all required elements");
            return true;

        } catch (error) {
            await this.logTest("Diamond List Page", false, `Error: ${error.message}`);
            return false;
        }
    }

    async testJewelryPageDiamondDisplay() {
        try {
            await this.page.goto(`${this.baseUrl}/jewelry`);
            await this.page.waitForSelector('.jewelry-list, [class*="jewelry"], .card', { timeout: 10000 });

            // Check if jewelry items are displayed
            const jewelryItems = await this.page.$$('.card, [class*="jewelry-card"]');
            if (jewelryItems.length === 0) {
                await this.logTest("Jewelry Page Diamond Display", true, "No jewelry items to test (empty state is OK)");
                return true;
            }

            // Check for jewelry details that should show diamond information
            const firstItem = jewelryItems[0];
            if (firstItem) {
                await firstItem.click();
                
                // Wait for details modal or navigation
                await this.page.waitForTimeout(1000);
                
                // Check if diamond information is displayed
                const hasDiamondInfo = await this.page.$eval('body', body => {
                    const text = body.textContent.toLowerCase();
                    return text.includes('diamond') || text.includes('carat') || text.includes('certificate');
                });

                if (!hasDiamondInfo) {
                    await this.logTest("Jewelry Page Diamond Display", false, "Diamond information not displayed in jewelry details");
                    return false;
                }
            }

            await this.logTest("Jewelry Page Diamond Display", true, "Jewelry page displays diamond information correctly");
            return true;

        } catch (error) {
            await this.logTest("Jewelry Page Diamond Display", false, `Error: ${error.message}`);
            return false;
        }
    }

    async testDiamondFormFunctionality() {
        try {
            await this.page.goto(`${this.baseUrl}/diamonds`);
            await this.page.waitForSelector('button:has-text("Add"), button[class*="add"]', { timeout: 10000 });

            // Click add diamond button
            const addButton = await this.page.$('button:has-text("Add"), button[class*="add"]');
            await addButton.click();

            // Wait for form modal
            await this.page.waitForSelector('form, .modal form', { timeout: 5000 });

            // Check for required form fields
            const requiredFields = [
                'input[name*="carat"], input[id*="carat"]',
                'select[name*="shape"], select[id*="shape"]',
                'input[name*="color"], select[name*="color"]',
                'input[name*="clarity"], select[name*="clarity"]',
                'input[name*="certificate"], input[id*="certificate"]'
            ];

            let missingFields = [];
            for (const fieldSelector of requiredFields) {
                const field = await this.page.$(fieldSelector);
                if (!field) {
                    missingFields.push(fieldSelector);
                }
            }

            if (missingFields.length > 0) {
                await this.logTest("Diamond Form Functionality", false, 
                    `Missing form fields: ${missingFields.join(', ')}`);
                return false;
            }

            // Check for quantity and stock management fields
            const stockFields = await this.page.$('input[name*="quantity"], input[id*="quantity"]');
            if (!stockFields) {
                await this.logTest("Diamond Form Functionality", false, "Quantity field not found");
                return false;
            }

            await this.logTest("Diamond Form Functionality", true, "Diamond form has all required fields");
            return true;

        } catch (error) {
            await this.logTest("Diamond Form Functionality", false, `Error: ${error.message}`);
            return false;
        }
    }

    async testResponsiveDesign() {
        try {
            // Test mobile viewport
            await this.page.setViewport({ width: 375, height: 667 });
            await this.page.goto(`${this.baseUrl}/diamonds`);
            await this.page.waitForSelector('body', { timeout: 5000 });

            // Check if layout adapts to mobile
            const isMobileResponsive = await this.page.$eval('body', body => {
                const width = window.innerWidth;
                return width <= 768; // Should be mobile width
            });

            if (!isMobileResponsive) {
                await this.logTest("Responsive Design", false, "Mobile viewport not properly set");
                return false;
            }

            // Test tablet viewport
            await this.page.setViewport({ width: 768, height: 1024 });
            await this.page.reload();
            await this.page.waitForSelector('body', { timeout: 5000 });

            // Test desktop viewport
            await this.page.setViewport({ width: 1280, height: 720 });
            await this.page.reload();
            await this.page.waitForSelector('body', { timeout: 5000 });

            await this.logTest("Responsive Design", true, "All viewport sizes work correctly");
            return true;

        } catch (error) {
            await this.logTest("Responsive Design", false, `Error: ${error.message}`);
            return false;
        }
    }

    async runAllTests() {
        console.log('🧪 COMPREHENSIVE DIAMOND FRONTEND TESTING');
        console.log('='.repeat(50));

        const setupSuccess = await this.setup();
        if (!setupSuccess) {
            return false;
        }

        const tests = [
            this.testDashboardDiamondDisplay,
            this.testDiamondListPage,
            this.testJewelryPageDiamondDisplay,
            this.testDiamondFormFunctionality,
            this.testResponsiveDesign
        ];

        for (const test of tests) {
            try {
                await test.call(this);
            } catch (error) {
                await this.logTest(test.name, false, `Test execution failed: ${error.message}`);
            }
        }

        await this.cleanup();

        // Print summary
        console.log('\n' + '='.repeat(50));
        console.log('📊 TEST SUMMARY');
        console.log('='.repeat(50));

        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(result => result.success).length;
        const failedTests = totalTests - passedTests;

        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests}`);
        console.log(`Failed: ${failedTests}`);
        console.log(`Success Rate: ${((passedTests/totalTests)*100).toFixed(1)}%`);

        if (failedTests > 0) {
            console.log('\n❌ FAILED TESTS:');
            this.testResults.filter(result => !result.success).forEach(result => {
                console.log(`  - ${result.test}: ${result.message}`);
            });
        }

        return failedTests === 0;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    (async () => {
        const tester = new DiamondFrontendTester();
        const success = await tester.runAllTests();
        process.exit(success ? 0 : 1);
    })();
}

module.exports = DiamondFrontendTester;
