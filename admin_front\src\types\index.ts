export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface Diamond {
  id: number;
  shape: string;
  shape_id: number;

  // Basic Properties
  carat: number;

  // 4Cs - Industry Standard
  color: string; // D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z
  clarity: string; // FL, IF, VVS1, VVS2, VS1, VS2, SI1, SI2, SI3, I1, I2, I3
  cut_grade?: string; // Excellent, Very Good, Good, Fair, Poor

  // Additional Grading
  polish?: string;
  symmetry?: string;
  fluorescence?: string;
  fluorescence_color?: string;

  // Measurements
  length_mm?: number;
  width_mm?: number;
  depth_mm?: number;
  depth_percent?: number;
  table_percent?: number;
  girdle?: string;
  culet?: string;

  // Certification
  certificate_no: string;
  certification_lab?: string;
  certificate_date?: string;
  certificate_url?: string;

  // Pricing
  cost_price?: number;
  retail_price?: number;
  market_value?: number;
  last_valuation_date?: string;

  // Inventory
  quantity: number;
  reserved_quantity?: number;
  available_quantity?: number;
  minimum_stock?: number;

  // Status and Location
  status: 'in_stock' | 'reserved' | 'sold' | 'manufacturing' | 'damaged' | 'lost';
  location?: string;
  notes?: string;

  // Relationships
  vendor_id?: number;
  vendorName?: string;

  // Dates
  purchase_date: string;
  created_at?: string;
  updated_at?: string;

  // Calculated Fields
  profit_margin?: number;
  profit_amount?: number;
  is_low_stock?: boolean;

  // Legacy
  size_mm?: string; // Deprecated, use length_mm, width_mm, depth_mm instead
}

export interface Vendor {
  id: number;
  name: string;
  company_name?: string;
  vendor_code?: string;

  // Contact Information
  contact_person?: string;
  contact_number: string;
  alternate_contact?: string;
  email?: string;
  website?: string;

  // Address Information
  address: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;

  // Business Information
  gst_number: string;
  pan_number?: string;
  business_type?: string;
  specialization?: string;

  // Financial Information
  credit_limit?: number;
  payment_terms?: string;
  bank_name?: string;
  bank_account?: string;
  ifsc_code?: string;

  // Performance Metrics
  rating?: number;
  total_orders?: number;
  total_value?: number;

  // Status and Verification
  status?: 'active' | 'inactive' | 'blacklisted' | 'pending_verification';
  is_verified?: boolean;
  verification_date?: string;

  // Additional Information
  notes?: string;
  documents_path?: string;

  // Timestamps
  created_at?: string;
  updated_at?: string;
  last_order_date?: string;

  // Calculated Fields
  outstanding_amount?: number;
}

export interface ManufacturingRequest {
  id: number;
  order_number?: string;
  vendor_id: number;
  vendor?: Vendor;

  // Order Details
  order_type?: string;
  priority?: 'urgent' | 'high' | 'normal' | 'low';
  description?: string;
  special_instructions?: string;

  // Dates and Timeline
  sent_date: string;
  expected_return_date: string;
  actual_return_date?: string;
  promised_delivery_date?: string;

  // Status Tracking
  status: 'draft' | 'sent' | 'in_progress' | 'quality_check' | 'completed' | 'cancelled' | 'returned' | 'on_hold';
  progress_percentage?: number;

  // Quality and Inspection
  quality_check_status?: 'passed' | 'failed' | 'pending' | 'needs_rework';
  quality_notes?: string;
  inspector_name?: string;
  inspection_date?: string;

  // Financial Information
  estimated_cost?: number;
  actual_cost?: number;
  advance_paid?: number;
  balance_amount?: number;
  payment_status?: 'pending' | 'partial' | 'paid' | 'overdue';

  // Weight Tracking
  total_original_weight?: number;
  total_final_weight?: number;
  total_loss_weight?: number;
  loss_percentage?: number;

  // Additional Information
  notes?: string;
  internal_notes?: string;
  images_path?: string;

  // Tracking and Audit
  created_by?: string;
  updated_by?: string;
  created_at?: string;
  updated_at?: string;

  // Relationships
  diamonds: Array<{
    diamond_id: number;
    diamond?: Diamond;
    quantity: number;
    original_weight?: number;
    final_weight?: number;
    loss_weight?: number;
    notes?: string;
  }>;

  // Calculated Fields
  is_overdue?: boolean;
  days_remaining?: number;

  // Legacy fields for backward compatibility
  return_date?: string; // Use actual_return_date instead
}

export interface Jewelry {
  id: number;
  name: string;
  design_code: string;
  sku?: string;
  vendor_id: number;
  vendor?: Vendor;

  // Product Details
  category?: string;
  subcategory?: string;
  collection?: string;
  style?: string;
  gender?: string;

  // Physical Specifications
  gross_weight: number;
  net_weight?: number;
  metal_type: string;
  metal_purity?: string;
  metal_color?: string;

  // Size and Dimensions
  size?: string;
  length_mm?: number;
  width_mm?: number;
  height_mm?: number;

  // Pricing and Cost
  cost_price?: number;
  making_charges?: number;
  retail_price?: number;
  discount_price?: number;
  profit_margin?: number;

  // Inventory Management
  quantity?: number;
  minimum_stock?: number;
  location?: string;
  barcode?: string;

  // Status and Lifecycle
  status: 'in_stock' | 'sold' | 'reserved' | 'on_hold' | 'damaged' | 'under_repair' | 'discontinued' | 'out_of_stock';
  condition?: 'new' | 'used' | 'refurbished' | 'vintage' | 'antique';
  availability?: 'available' | 'reserved' | 'sold' | 'on_hold';

  // Dates
  received_date: string;
  manufactured_date?: string;
  last_sold_date?: string;
  last_maintenance_date?: string;

  // Certification and Documentation
  certificate_number?: string;
  hallmark_number?: string;
  appraisal_value?: number;
  appraisal_date?: string;

  // Quality and Features
  finish?: string;
  setting_style?: string;
  clasp_type?: string;
  special_features?: string;

  // Care and Maintenance
  care_instructions?: string;
  warranty_period?: number;
  warranty_terms?: string;

  // Marketing and Sales
  description?: string;
  tags?: string;
  is_featured?: boolean;
  is_bestseller?: boolean;
  seo_title?: string;
  seo_description?: string;

  // Images and Media
  image_path?: string | null;
  additional_images?: string;
  video_url?: string;

  // Analytics and Performance
  view_count?: number;
  inquiry_count?: number;
  times_sold?: number;
  rating?: number;
  review_count?: number;

  // Additional Information
  notes?: string;
  custom_fields?: string;

  // Tracking and Audit
  created_by?: string;
  updated_by?: string;
  created_at?: string;
  updated_at?: string;

  // Relationships
  diamonds?: Array<{
    diamond_id: number;
    quantity: number;
    diamond?: Diamond;
    setting_type?: string;
    position?: string;
    notes?: string;
  }>;

  // Calculated Fields
  is_low_stock?: boolean;
  total_cost?: number;
  effective_price?: number;
}

export interface Sale {
  id: number;
  invoice_no: string;
  order_number?: string;

  // Customer Information
  customer_name: string;
  customer_email?: string;
  customer_phone?: string;
  customer_address?: string;
  customer_city?: string;
  customer_state?: string;
  customer_postal_code?: string;
  customer_country?: string;

  // Sale Details
  sale_date: string;
  sale_time?: string;
  sale_type?: 'retail' | 'wholesale' | 'online' | 'custom' | 'exhibition' | 'b2b';
  sales_channel?: 'store' | 'online' | 'phone' | 'exhibition' | 'home_visit' | 'referral';

  // Pricing and Amounts
  subtotal: number;
  discount_percentage?: number;
  discount_amount?: number;
  tax_percentage?: number;
  tax_amount?: number;
  shipping_charges?: number;
  other_charges?: number;
  total_amount: number;

  // Payment Information
  payment_status: 'pending' | 'partial' | 'paid' | 'refunded' | 'cancelled' | 'overdue';
  payment_method?: 'cash' | 'card' | 'upi' | 'bank_transfer' | 'cheque' | 'emi' | 'crypto';
  payment_terms?: string;
  amount_paid?: number;
  balance_amount?: number;

  // Payment Details
  transaction_id?: string;
  payment_reference?: string;
  payment_date?: string;
  due_date?: string;

  // Delivery Information
  delivery_method?: 'pickup' | 'home_delivery' | 'courier' | 'express' | 'international';
  delivery_address?: string;
  delivery_date?: string;
  actual_delivery_date?: string;
  delivery_status?: 'pending' | 'packed' | 'shipped' | 'in_transit' | 'delivered' | 'returned';
  tracking_number?: string;
  delivery_charges?: number;

  // Sales Representative
  sales_person?: string;
  sales_commission?: number;
  commission_percentage?: number;

  // Status and Workflow
  status?: 'draft' | 'confirmed' | 'processing' | 'completed' | 'cancelled' | 'returned' | 'exchanged' | 'refunded';
  priority?: 'urgent' | 'high' | 'normal' | 'low';

  // Return and Exchange
  is_returnable?: boolean;
  return_period?: number;
  exchange_policy?: string;

  // Gift and Special Services
  is_gift?: boolean;
  gift_message?: string;
  gift_wrapping?: boolean;
  gift_wrapping_charges?: number;

  // Insurance and Warranty
  insurance_required?: boolean;
  insurance_amount?: number;
  warranty_period?: number;
  extended_warranty?: boolean;

  // Additional Information
  notes?: string;
  internal_notes?: string;
  special_instructions?: string;

  // Marketing and Analytics
  source?: string;
  campaign?: string;
  coupon_code?: string;

  // Tracking and Audit
  created_by?: string;
  updated_by?: string;
  created_at?: string;
  updated_at?: string;

  // Relationships
  jewelry_items?: Array<{
    jewelry_id: number;
    jewelry?: Jewelry;
    quantity: number;
    unit_price: number;
    discount_amount?: number;
    tax_amount?: number;
    total_amount: number;
  }>;

  // Legacy fields for backward compatibility
  jewelry_id?: number;
  jewelry?: Jewelry;

  // Calculated Fields
  is_overdue?: boolean;
  days_overdue?: number;
  can_be_returned?: boolean;
}

export interface DashboardActivity {
  type: string;
  description: string;
  date: string;
  user?: string;
  details?: any;
}

export interface DashboardSummary {
  // Diamond Counts
  diamonds_in_stock: number;
  total_diamonds: number;
  diamonds_sold: number;
  diamonds_manufacturing: number;
  diamonds_reserved: number;

  // Other Counts
  jewelry_in_stock: number;
  jewelry_sold: number;
  total_sales: number;
  open_manufacturing: number;
  completed_manufacturing: number;
  total_inventory_value: number;

  // Enhanced Metrics
  total_vendors: number;
  active_customers: number;
  pending_orders: number;
  overdue_payments: number;
  low_stock_items: number;
  out_of_stock_items: number;

  // Performance Indicators
  conversion_rate?: number;
  average_order_value?: number;
  customer_satisfaction?: number;

  // Recent Activities
  recent_activities?: DashboardActivity[];
}

export interface SalesStats {
  // Basic Sales
  total_sales: number;
  paid_sales: number;
  unpaid_sales: number;

  // Time-based Sales
  today_sales: number;
  yesterday_sales: number;
  this_week_sales: number;
  last_week_sales: number;
  this_month_sales: number;
  last_month_sales: number;

  // Growth Metrics
  daily_growth: number;
  weekly_growth: number;
  monthly_growth: number;

  // Performance
  average_order_value: number;
  total_orders: number;
  completed_orders: number;
  cancelled_orders: number;

  // Revenue
  total_revenue: number;
  pending_payments: number;
  overdue_payments: number;
}

export interface StockLevels {
  diamonds_in_stock: number;
  jewelry_in_stock: number;

  // Stock Alerts
  low_stock_diamonds: number;
  out_of_stock_diamonds: number;
  low_stock_jewelry: number;
  out_of_stock_jewelry: number;

  // Inventory Values
  diamonds_inventory_value: number;
  jewelry_inventory_value: number;

  // Stock Movement
  fast_moving_items: number;
  slow_moving_items: number;
}

export interface ManufacturingStats {
  total_orders: number;
  pending_orders: number;
  in_progress_orders: number;
  completed_orders: number;
  overdue_orders: number;
  average_completion_time: number;
  on_time_delivery_rate: number;
  quality_pass_rate: number;
}

export interface TrendData {
  period: string;
  sales: number;
  orders: number;
  revenue: number;
}