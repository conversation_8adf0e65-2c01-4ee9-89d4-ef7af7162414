import React, { useState, useEffect, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Edit, Trash2, Minus, Link2, Filter, X, Search, Grid, List, QrCode, Tag } from 'lucide-react';
import { api } from '../../lib/api';
import { Diamond } from '../../types';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import Modal from '../../components/ui/Modal';
import { LoadingState, ErrorState, EmptyState } from '../../components/DataStates';
import LoadingSkeleton from '../../components/ui/LoadingSkeleton';
import DiamondForm from './DiamondForm';
import DeductStockForm from './DeductStockForm';
import toast from 'react-hot-toast';
import { saveAs } from 'file-saver';
import {
  DIAMOND_COLORS,
  DIAMOND_CLARITIES,
  CUT_GRADES,
  POLISH_SYMMETRY_GRADES,
  FLUORESCENCE_INTENSITIES,
  DIAMOND_STATUSES,
  CERTIFICATION_LABS,
  getColorGrade,
  getClarityGrade,
  getStatusLabel
} from '../../constants/diamond';
import { format } from 'date-fns';
import SearchInput from '../../components/ui/SearchInput';
import { useSearch } from '../../hooks/useSearch';

// Utility to convert array of objects to CSV
function diamondsToCSV(diamonds: Diamond[]): string {
  const headers = [
    'ID', 'Shape', 'Size (mm)', 'Carat', 'Clarity', 'Color', 'Certificate No.', 'Quantity', 'Purchase Date', 'Status', 'Vendor'
  ];
  const rows = diamonds.map(d => [
    d.id,
    d.shape,
    d.size_mm || 'N/A',
    d.carat,
    d.clarity,
    d.color,
    d.certificate_no,
    d.quantity,
    d.purchase_date,
    d.status,
    d.vendorName || ''
  ]);
  return [headers, ...rows].map(r => r.map(x => `"${x ?? ''}"`).join(',')).join('\n');
}

const DiamondList: React.FC = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeductModalOpen, setIsDeductModalOpen] = useState(false);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [selectedDiamond, setSelectedDiamond] = useState<Diamond | null>(null);
  // Search and pagination
  const search = useSearch({
    onSearch: (query) => {
      setPage(1); // Reset to first page when searching
    }
  });
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [sortBy, setSortBy] = useState('');
  const [sortDir, setSortDir] = useState<'asc' | 'desc'>('asc');

  // Basic filters
  const [shapeFilter, setShapeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [vendorFilter, setVendorFilter] = useState('');

  // 4Cs filters
  const [colorFilter, setColorFilter] = useState('');
  const [clarityFilter, setClarityFilter] = useState('');
  const [cutGradeFilter, setCutGradeFilter] = useState('');
  const [caratMin, setCaratMin] = useState('');
  const [caratMax, setCaratMax] = useState('');

  // Additional grading filters
  const [polishFilter, setPolishFilter] = useState('');
  const [symmetryFilter, setSymmetryFilter] = useState('');
  const [fluorescenceFilter, setFluorescenceFilter] = useState('');

  // Certification filters
  const [certificationLabFilter, setCertificationLabFilter] = useState('');
  const [certificateNoFilter, setCertificateNoFilter] = useState('');

  // Pricing filters
  const [priceMin, setPriceMin] = useState('');
  const [priceMax, setPriceMax] = useState('');
  const [costPriceMin, setCostPriceMin] = useState('');
  const [costPriceMax, setCostPriceMax] = useState('');

  // Inventory filters
  const [quantityMin, setQuantityMin] = useState('');
  const [lowStockOnly, setLowStockOnly] = useState(false);
  const [locationFilter, setLocationFilter] = useState('');

  // Advanced filters
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Modal states
  const [isDeductConfirmOpen, setIsDeductConfirmOpen] = useState(false);
  const [pendingDeductDiamond, setPendingDeductDiamond] = useState<Diamond | null>(null);
  const [selectedManufacturing, setSelectedManufacturing] = useState<number | null>(null);
  const [selectedJewelry, setSelectedJewelry] = useState<number | null>(null);

  // Bulk selection states
  const [selectedDiamonds, setSelectedDiamonds] = useState<Set<number>>(new Set());
  const [isBulkActionsOpen, setIsBulkActionsOpen] = useState(false);
  const [isBulkEditOpen, setIsBulkEditOpen] = useState(false);
  const [isBulkStatusOpen, setIsBulkStatusOpen] = useState(false);
  const [bulkEditData, setBulkEditData] = useState<any>({});

  // Column visibility states
  const [visibleColumns, setVisibleColumns] = useState<{[key: string]: boolean}>({
    shape: true,
    carat: true,
    color: true,
    quantity: true,
    price: true,
    status: true,
    vendor: true,
    purchase_date: true,
    actions: true
  });
  const [isColumnSelectorOpen, setIsColumnSelectorOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');
  const columnSelectorRef = useRef<HTMLDivElement>(null);

  // Click outside handler for column selector
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (columnSelectorRef.current && !columnSelectorRef.current.contains(event.target as Node)) {
        setIsColumnSelectorOpen(false);
      }
    };

    if (isColumnSelectorOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isColumnSelectorOpen]);

  const queryClient = useQueryClient();

  // No more debouncing - search only triggers on Enter or button click

  // Enhanced error handling for diamond fetching with comprehensive filters
  const {
    data: diamondsRaw,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['diamonds', {
      search: search.activeSearchQuery,
      shape: shapeFilter,
      status: statusFilter,
      color: colorFilter,
      clarity: clarityFilter,
      cut_grade: cutGradeFilter,
      polish: polishFilter,
      symmetry: symmetryFilter,
      fluorescence: fluorescenceFilter,
      certification_lab: certificationLabFilter,
      certificate_no: certificateNoFilter,
      page,
      limit: pageSize,
      min_carat: caratMin,
      max_carat: caratMax,
      min_price: priceMin,
      max_price: priceMax,
      min_cost_price: costPriceMin,
      max_cost_price: costPriceMax,
      min_quantity: quantityMin,
      low_stock_only: lowStockOnly,
      location: locationFilter,
      vendor_id: vendorFilter,
      sort_by: sortBy,
      sort_dir: sortDir
    }],
    queryFn: async (): Promise<{ data: Diamond[]; total: number; page: number; limit: number }> => {
      const params: any = {};

      // Search and basic filters
      if (search.activeSearchQuery && search.activeSearchQuery.length > 1) params.search = search.activeSearchQuery;
      if (shapeFilter) params.shape = shapeFilter;
      if (statusFilter) params.status = statusFilter;
      if (vendorFilter) params.vendor_id = vendorFilter;

      // 4Cs filters
      if (colorFilter) params.color = colorFilter;
      if (clarityFilter) params.clarity = clarityFilter;
      if (cutGradeFilter) params.cut_grade = cutGradeFilter;
      if (caratMin) params.min_carat = caratMin;
      if (caratMax) params.max_carat = caratMax;

      // Additional grading filters
      if (polishFilter) params.polish = polishFilter;
      if (symmetryFilter) params.symmetry = symmetryFilter;
      if (fluorescenceFilter) params.fluorescence = fluorescenceFilter;

      // Certification filters
      if (certificationLabFilter) params.certification_lab = certificationLabFilter;
      if (certificateNoFilter) params.certificate_no = certificateNoFilter;

      // Pricing filters
      if (priceMin) params.min_price = priceMin;
      if (priceMax) params.max_price = priceMax;
      if (costPriceMin) params.min_cost_price = costPriceMin;
      if (costPriceMax) params.max_cost_price = costPriceMax;

      // Inventory filters
      if (quantityMin) params.min_quantity = quantityMin;
      if (lowStockOnly) params.low_stock_only = 'true';
      if (locationFilter) params.location = locationFilter;

      // Pagination and sorting
      params.page = page;
      params.limit = pageSize;
      if (vendorFilter) params.vendor_id = vendorFilter;
      if (sortBy) {
        params.sort_by = sortBy;
        params.sort_dir = sortDir;
      }
      try {
        const response = await api.diamonds.list(params);
        if (!response || !response.data) {
          return { data: [], total: 0, page: 1, limit: pageSize };
        }
        if (Array.isArray(response.data)) {
          return { data: response.data, total: response.data.length, page: 1, limit: pageSize };
        }
        if (Array.isArray(response.data.data)) {
          return response.data;
        }
        return { data: [], total: 0, page: 1, limit: pageSize };
      } catch (err: any) {
        if (err.response?.status === 401) {
          toast.error('Unauthorized: Please log in again.');
        } else {
          toast.error('Failed to load diamonds. Please try again later.');
        }
        throw err;
      }
    },
    retry: 2
  });

  // Enhanced error handling for API calls
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      await api.diamonds.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['diamonds'] });
      toast.success('Diamond deleted successfully');
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to delete diamond';
      toast.error(`Error: ${errorMessage}`);
    }
  });

  // Bulk operations mutations
  const bulkUpdateMutation = useMutation({
    mutationFn: ({ diamond_ids, updates }: { diamond_ids: number[], updates: any }) =>
      api.diamonds.bulkUpdate(diamond_ids, updates),
    onSuccess: (data: any) => {
      queryClient.invalidateQueries({ queryKey: ['diamonds'] });
      toast.success(`Successfully updated ${data.updated_count} diamonds`);
      setSelectedDiamonds(new Set());
      setIsBulkEditOpen(false);
      setIsBulkStatusOpen(false);
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to update diamonds';
      toast.error(`Error: ${errorMessage}`);
    }
  });

  const bulkDeleteMutation = useMutation({
    mutationFn: (diamond_ids: number[]) => api.diamonds.bulkDelete(diamond_ids),
    onSuccess: (data: any) => {
      queryClient.invalidateQueries({ queryKey: ['diamonds'] });
      toast.success(`Successfully deleted ${data.deleted_count} diamonds`);
      setSelectedDiamonds(new Set());
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to delete diamonds';
      toast.error(`Error: ${errorMessage}`);
    }
  });

  const assignManufacturingMutation = useMutation({
    mutationFn: async () => {
      if (!selectedDiamond || !selectedManufacturing) {
        throw new Error('Please select both diamond and manufacturing');
      }
      try {
        await api.diamonds.assignToManufacturing(selectedDiamond.id, selectedManufacturing);
      } catch (error: any) {
        if (error.response?.status === 404) {
          throw new Error('Manufacturing ID not found. Please select a valid manufacturing option.');
        }
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['diamonds'] });
      toast.success('Diamond assigned to manufacturing successfully');
      setIsAssignModalOpen(false);
      setSelectedDiamond(null);
      setSelectedManufacturing(null);
    },
    onError: (error: any) => {
      const errorMessage = error.message || 'Failed to assign diamond to manufacturing';
      toast.error(errorMessage);
    },
  });

  const assignJewelryMutation = useMutation({
    mutationFn: async () => {
      if (!selectedDiamond || !selectedJewelry) {
        throw new Error('Please select both diamond and jewelry');
      }
      await api.diamonds.assignToJewelry(selectedDiamond.id, selectedJewelry);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['diamonds'] });
      toast.success('Diamond assigned to jewelry successfully');
      setIsAssignModalOpen(false);
      setSelectedDiamond(null);
      setSelectedJewelry(null);
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to assign diamond to jewelry';
      toast.error(errorMessage);
    },
  });

  const handleEdit = (diamond: Diamond) => {
    setSelectedDiamond(diamond);
    setIsEditModalOpen(true);
  };

  const handleDeduct = (diamond: Diamond) => {
    setPendingDeductDiamond(diamond);
    setIsDeductConfirmOpen(true);
  };

  const confirmDeduct = () => {
    if (pendingDeductDiamond) {
      setSelectedDiamond(pendingDeductDiamond);
      setIsDeductModalOpen(true);
    }
    setIsDeductConfirmOpen(false);
    setPendingDeductDiamond(null);
  };

  const cancelDeduct = () => {
    setIsDeductConfirmOpen(false);
    setPendingDeductDiamond(null);
  };

  const handleAssign = (diamond: Diamond) => {
    setSelectedDiamond(diamond);
    setSelectedManufacturing(null);
    setSelectedJewelry(null);
    setIsAssignModalOpen(true);
  };

  const handleGenerateQRCode = async (diamond: Diamond) => {
    try {
      const response = await api.diamonds.generateQRCode(diamond.id);

      // Create a new window to display the QR code
      const newWindow = window.open('', '_blank', 'width=400,height=500');
      if (newWindow) {
        newWindow.document.write(`
          <html>
            <head>
              <title>QR Code - Diamond ${diamond.id}</title>
              <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                .qr-container { margin: 20px 0; }
                .diamond-info { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 8px; }
                .print-btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 10px; }
                .print-btn:hover { background: #0056b3; }
                @media print { .no-print { display: none; } }
              </style>
            </head>
            <body>
              <h2>Diamond QR Code</h2>
              <div class="diamond-info">
                <h3>Diamond #${diamond.id}</h3>
                <p><strong>Shape:</strong> ${diamond.shape}</p>
                <p><strong>Carat:</strong> ${diamond.carat}</p>
                <p><strong>Color:</strong> ${diamond.color}</p>
                <p><strong>Clarity:</strong> ${diamond.clarity}</p>
                <p><strong>Certificate:</strong> ${diamond.certificate_no}</p>
              </div>
              <div class="qr-container">
                <img src="${response.qr_code}" alt="QR Code" style="max-width: 200px; height: auto;" />
              </div>
              <div class="no-print">
                <button class="print-btn" onclick="window.print()">Print QR Code</button>
                <button class="print-btn" onclick="window.close()">Close</button>
              </div>
            </body>
          </html>
        `);
        newWindow.document.close();
      }

      toast.success('QR Code generated successfully');
    } catch (error) {
      console.error('QR Code generation failed:', error);
      toast.error('Failed to generate QR Code');
    }
  };

  const handleGenerateLabel = async (diamond: Diamond) => {
    try {
      const response = await api.diamonds.generateLabel(diamond.id, true);

      // Create a new window to display the label
      const newWindow = window.open('', '_blank', 'width=500,height=400');
      if (newWindow) {
        newWindow.document.write(`
          <html>
            <head>
              <title>Label - Diamond ${diamond.id}</title>
              <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                .label-container { margin: 20px 0; }
                .print-btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 10px; }
                .print-btn:hover { background: #0056b3; }
                @media print { .no-print { display: none; } }
              </style>
            </head>
            <body>
              <h2>Diamond Label</h2>
              <div class="label-container">
                <img src="${response.label}" alt="Diamond Label" style="max-width: 300px; height: auto; border: 1px solid #ccc;" />
              </div>
              <div class="no-print">
                <button class="print-btn" onclick="window.print()">Print Label</button>
                <button class="print-btn" onclick="window.close()">Close</button>
              </div>
            </body>
          </html>
        `);
        newWindow.document.close();
      }

      toast.success('Label generated successfully');
    } catch (error) {
      console.error('Label generation failed:', error);
      toast.error('Failed to generate label');
    }
  };

  const handleDelete = async (diamond: Diamond) => {
    if (window.confirm('Are you sure you want to delete this diamond? This action cannot be undone.')) {
      deleteMutation.mutate(diamond.id);
    }
  };

  const handleExportCSV = async () => {
    try {
      // Show loading toast
      const loadingToast = toast.loading('Exporting diamonds...');

      // Build params object with all current filters
      const params: any = {};

      // Search and basic filters
      if (debouncedSearch && debouncedSearch.length > 1) params.search = debouncedSearch;
      if (shapeFilter) params.shape = shapeFilter;
      if (statusFilter) params.status = statusFilter;
      if (vendorFilter) params.vendor_id = vendorFilter;

      // 4Cs filters
      if (colorFilter) params.color = colorFilter;
      if (clarityFilter) params.clarity = clarityFilter;
      if (cutGradeFilter) params.cut_grade = cutGradeFilter;
      if (caratMin) params.min_carat = caratMin;
      if (caratMax) params.max_carat = caratMax;

      // Additional grading filters
      if (polishFilter) params.polish = polishFilter;
      if (symmetryFilter) params.symmetry = symmetryFilter;
      if (fluorescenceFilter) params.fluorescence = fluorescenceFilter;

      // Certification filters
      if (certificationLabFilter) params.certification_lab = certificationLabFilter;
      if (certificateNoFilter) params.certificate_no = certificateNoFilter;

      // Pricing filters
      if (priceMin) params.min_price = priceMin;
      if (priceMax) params.max_price = priceMax;
      if (costPriceMin) params.min_cost_price = costPriceMin;
      if (costPriceMax) params.max_cost_price = costPriceMax;

      // Inventory filters
      if (quantityMin) params.min_quantity = quantityMin;
      if (lowStockOnly) params.low_stock_only = 'true';
      if (locationFilter) params.location = locationFilter;

      // Call the export API
      const response = await api.diamonds.export(params);

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Create a blob from the response
      const blob = new Blob([response as BlobPart], { type: 'text/csv' });

      // Generate filename with current date
      const filename = `diamonds_export_${new Date().toISOString().slice(0,10)}.csv`;

      // Save the file
      saveAs(blob, filename);

      // Show success message
      toast.success('Diamonds exported successfully!');
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Failed to export diamonds. Please try again.');
    }
  };

  const { data: shapeOptions, isLoading: isLoadingShapes } = useQuery({
    queryKey: ['shapes'],
    queryFn: async () => {
      const response = await api.shapes.list();
      if (!response || !Array.isArray(response)) {
        throw new Error('Failed to fetch shapes');
      }
      return response.map((shape: { id: number; name: string }) => ({
        value: shape.id.toString(),
        label: shape.name
      }));
    },
  });

  const statusOptions = [
    { value: 'in_stock', label: 'In Stock' },
    { value: 'reserved', label: 'Reserved' },
    { value: 'used', label: 'Used' }
  ];

  const { data: vendorOptions, isLoading: isLoadingVendors } = useQuery({
    queryKey: ['vendors'],
    queryFn: async () => {
      const response = await api.vendors.list();
      const arr = Array.isArray(response) ? response : response?.data;
      if (!Array.isArray(arr)) throw new Error('Failed to fetch vendors');
      return arr.map((vendor: { id: number; name: string }) => ({
        value: vendor.id.toString(),
        label: vendor.name
      }));
    },
  });

  const {
    data: manufacturingTypes
  } = useQuery({
    queryKey: ['manufacturing-types'],
    queryFn: async (): Promise<{ id: number; name: string }[]> => {
      const response = await api.manufacturing.types.list();
      if (!response || !Array.isArray(response)) {
        throw new Error('Failed to fetch manufacturing types');
      }
      return response;
    },
    retry: 1
  });

  const {
    data: jewelryOptions
  } = useQuery({
    queryKey: ['jewelry-options'],
    queryFn: async (): Promise<{ id: number; name: string }[]> => {
      try {
        const response = await api.jewelry.list({ status: 'in_stock' });
        if (!response || !response.data) {
          return [];
        }
        return response.data;
      } catch (error) {
        console.warn('Failed to fetch jewelry options:', error);
        return [];
      }
    },
    retry: 1
  });

  // Remove all client-side filtering and sorting
  const filteredDiamonds = diamondsRaw?.data || [];

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Header skeleton */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <div className="h-8 bg-gray-200 rounded w-48 mb-2 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-64 animate-pulse"></div>
          </div>
          <div className="flex gap-2">
            <div className="h-10 bg-gray-200 rounded w-24 animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
          </div>
        </div>

        {/* Filters skeleton */}
        <Card>
          <div className="space-y-4">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-10 bg-gray-200 rounded animate-pulse"></div>
              ))}
            </div>
          </div>
        </Card>

        {/* Table skeleton */}
        <Card>
          <LoadingSkeleton rows={10} columns={9} />
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <ErrorState
        title="Failed to load diamonds"
        description="Unable to load diamond inventory. Please check your connection and try again."
        onRetry={refetch}
        error={error as Error}
      />
    );
  }

  const totalDiamonds = diamondsRaw?.total ?? 0;
  let totalPages = Math.ceil(totalDiamonds / pageSize);
  if (!isFinite(totalPages) || totalPages < 1) totalPages = 1;

  // Card view for mobile
  const renderCardView = (diamonds: Diamond[]) => {
    if (!Array.isArray(diamonds)) {
      return null;
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {diamonds.map((diamond) => (
          <Card key={diamond.id} className={`p-4 ${selectedDiamonds.has(diamond.id) ? 'ring-2 ring-blue-500' : ''}`}>
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  checked={selectedDiamonds.has(diamond.id)}
                  onChange={(e) => {
                    const newSelected = new Set(selectedDiamonds);
                    if (e.target.checked) {
                      newSelected.add(diamond.id);
                    } else {
                      newSelected.delete(diamond.id);
                    }
                    setSelectedDiamonds(newSelected);
                  }}
                />
                <h3 className="font-semibold text-gray-900">
                  {diamond.shape} {diamond.carat}ct
                </h3>
              </div>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                diamond.status === 'in_stock' ? 'bg-green-100 text-green-800' :
                diamond.status === 'reserved' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {diamond.status}
              </span>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Color:</span>
                <span className="font-medium">{diamond.color}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Clarity:</span>
                <span className="font-medium">{diamond.clarity}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Quantity:</span>
                <span className={`font-medium ${diamond.quantity <= 2 ? 'text-red-600' : ''}`}>
                  {diamond.quantity}
                </span>
              </div>
              {diamond.retail_price && (
                <div className="flex justify-between">
                  <span className="text-gray-500">Price:</span>
                  <span className="font-medium">
                    ${diamond.retail_price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-500">Certificate:</span>
                <span className="font-medium text-xs truncate max-w-24" title={diamond.certificate_no}>
                  {diamond.certificate_no}
                </span>
              </div>
              {diamond.vendorName && (
                <div className="flex justify-between">
                  <span className="text-gray-500">Vendor:</span>
                  <span className="font-medium">{diamond.vendorName}</span>
                </div>
              )}
            </div>

            <div className="flex justify-between items-center mt-4 pt-3 border-t">
              <div className="flex space-x-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleEdit(diamond)}
                  title="Edit"
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeduct(diamond)}
                  disabled={diamond.quantity === 0}
                  title="Deduct Stock"
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleAssign(diamond)}
                  title="Assign"
                >
                  <Link2 className="h-4 w-4 text-blue-600" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleGenerateQRCode(diamond)}
                  title="QR Code"
                >
                  <QrCode className="h-4 w-4 text-purple-600" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleGenerateLabel(diamond)}
                  title="Label"
                >
                  <Tag className="h-4 w-4 text-orange-600" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDelete(diamond)}
                  title="Delete"
                >
                  <Trash2 className="h-4 w-4 text-red-600" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  };

  // Refactored table row rendering logic with column visibility
  const renderTableRows = (diamonds: Diamond[]) => {
    if (!Array.isArray(diamonds)) {
      return null;
    }
    return diamonds.map((diamond) => (
      <tr key={diamond.id} className={`hover:bg-gray-50 ${selectedDiamonds.has(diamond.id) ? 'bg-blue-50' : ''}`}>
        <td className="px-6 py-4 whitespace-nowrap">
          <input
            type="checkbox"
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            checked={selectedDiamonds.has(diamond.id)}
            onChange={(e) => {
              const newSelected = new Set(selectedDiamonds);
              if (e.target.checked) {
                newSelected.add(diamond.id);
              } else {
                newSelected.delete(diamond.id);
              }
              setSelectedDiamonds(newSelected);
            }}
          />
        </td>
        {visibleColumns.shape && (
          <td className="px-6 py-4 whitespace-nowrap">
            <div>
              <div className="text-sm font-medium text-gray-900">{diamond.shape}</div>
              <div className="text-sm text-gray-500">{diamond.size_mm && diamond.size_mm.includes('*') ? diamond.size_mm : diamond.size_mm ? `${diamond.size_mm}mm` : 'N/A'}</div>
            </div>
          </td>
        )}
        {visibleColumns.carat && (
          <td className="px-6 py-4 whitespace-nowrap">
            <div>
              <div className="text-sm font-medium text-gray-900">{diamond.carat} ct</div>
              <div className="text-sm text-gray-500">{diamond.clarity}</div>
            </div>
          </td>
        )}
        {visibleColumns.color && (
          <td className="px-6 py-4 whitespace-nowrap">
            <div>
              <div className="text-sm font-medium text-gray-900">{diamond.color}</div>
              <div className="text-sm text-gray-500 truncate max-w-xs" title={diamond.certificate_no}>{diamond.certificate_no}</div>
            </div>
          </td>
        )}
        {visibleColumns.quantity && (
          <td className="px-6 py-4 whitespace-nowrap">
            <span className={`text-sm font-medium ${diamond.quantity <= 2 ? 'text-red-600 font-bold' : 'text-gray-900'}`}>{diamond.quantity}</span>
          </td>
        )}
        {visibleColumns.price && (
          <td className="px-6 py-4 whitespace-nowrap">
            <div>
              {diamond.retail_price ? (
                <div className="text-sm font-medium text-gray-900">
                  ${diamond.retail_price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </div>
              ) : (
                <div className="text-sm text-gray-500">Not set</div>
              )}
              {diamond.cost_price && (
                <div className="text-xs text-gray-500">
                  Cost: ${diamond.cost_price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </div>
              )}
            </div>
          </td>
        )}
        {visibleColumns.status && (
          <td className="px-6 py-4 whitespace-nowrap">
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              diamond.status === 'in_stock' ? 'bg-green-100 text-green-800' :
              diamond.status === 'reserved' ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {diamond.status}
            </span>
          </td>
        )}
        {visibleColumns.vendor && (
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="text-sm font-medium text-gray-900">{diamond.vendorName || 'N/A'}</div>
          </td>
        )}
        {visibleColumns.purchase_date && (
          <td className="px-6 py-4 whitespace-nowrap">
            <span className="text-sm text-gray-900">{diamond.purchase_date ? format(new Date(diamond.purchase_date), 'dd MMM yyyy') : '-'}</span>
          </td>
        )}
        {visibleColumns.actions && (
          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                aria-label="Edit diamond"
                onClick={() => handleEdit(diamond)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                aria-label="Deduct stock"
                onClick={() => handleDeduct(diamond)}
                disabled={diamond.quantity === 0 || deleteMutation.isPending}
                isLoading={deleteMutation.isPending && selectedDiamond?.id === diamond.id}
              >
                <Minus className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                aria-label="Assign to manufacturing or jewelry"
                onClick={() => handleAssign(diamond)}
                title="Assign to Manufacturing or Jewelry"
              >
                <Link2 className="h-4 w-4 text-blue-600" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                aria-label="Generate QR Code"
                onClick={() => handleGenerateQRCode(diamond)}
                title="Generate QR Code"
              >
                <QrCode className="h-4 w-4 text-purple-600" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                aria-label="Generate Label"
                onClick={() => handleGenerateLabel(diamond)}
                title="Generate Label"
              >
                <Tag className="h-4 w-4 text-orange-600" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                aria-label="Delete diamond"
                onClick={() => handleDelete(diamond)}
                disabled={deleteMutation.isPending}
                isLoading={deleteMutation.isPending && selectedDiamond?.id === diamond.id}
              >
                <Trash2 className="h-4 w-4 text-red-600" />
              </Button>
            </div>
          </td>
        )}
      </tr>
    ));
  };

  const manufacturingOptions = Array.isArray(manufacturingTypes) ? manufacturingTypes.map((type) => ({
    value: type.id.toString(),
    label: type.name
  })) : [];

  const jewelryOptionsMapped = Array.isArray(jewelryOptions) ? jewelryOptions.map((item) => ({
    value: item.id.toString(),
    label: item.name
  })) : [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Diamonds</h1>
          <p className="text-gray-600">Manage your diamond inventory</p>
        </div>
        <div className="flex gap-2">
          {/* View Mode Toggle */}
          <div className="flex border border-gray-300 rounded-md">
            <Button
              size="sm"
              variant={viewMode === 'table' ? 'primary' : 'ghost'}
              onClick={() => setViewMode('table')}
              className="rounded-r-none border-r-0"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant={viewMode === 'cards' ? 'primary' : 'ghost'}
              onClick={() => setViewMode('cards')}
              className="rounded-l-none"
            >
              <Grid className="h-4 w-4" />
            </Button>
          </div>

          <div className="relative" ref={columnSelectorRef}>
            <Button
              onClick={() => setIsColumnSelectorOpen(!isColumnSelectorOpen)}
              variant="secondary"
            >
              <Filter className="h-4 w-4 mr-2" />
              Columns
            </Button>
            {isColumnSelectorOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                <div className="py-1">
                  <div className="px-4 py-2 text-sm font-medium text-gray-700 border-b">
                    Show/Hide Columns
                  </div>
                  {Object.entries(visibleColumns).map(([column, visible]) => (
                    <label key={column} className="flex items-center px-4 py-2 hover:bg-gray-50 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={visible}
                        onChange={(e) => setVisibleColumns(prev => ({ ...prev, [column]: e.target.checked }))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
                      />
                      <span className="text-sm text-gray-700 capitalize">
                        {column.replace('_', ' ')}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>
          <Button onClick={handleExportCSV} variant="secondary">
            Export CSV
          </Button>
          <Button onClick={() => setIsAddModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Diamond
          </Button>
        </div>
      </div>

      {/* Bulk Actions Toolbar */}
      {selectedDiamonds.size > 0 && (
        <Card className="bg-blue-50 border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-blue-900">
                {selectedDiamonds.size} diamond{selectedDiamonds.size !== 1 ? 's' : ''} selected
              </span>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => setIsBulkEditOpen(true)}
                  disabled={bulkUpdateMutation.isPending}
                >
                  Bulk Edit
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => setIsBulkStatusOpen(true)}
                  disabled={bulkUpdateMutation.isPending}
                >
                  Update Status
                </Button>
                <Button
                  size="sm"
                  variant="danger"
                  onClick={() => {
                    if (window.confirm(`Are you sure you want to delete ${selectedDiamonds.size} selected diamonds? This action cannot be undone.`)) {
                      bulkDeleteMutation.mutate(Array.from(selectedDiamonds));
                    }
                  }}
                  disabled={bulkDeleteMutation.isPending}
                  isLoading={bulkDeleteMutation.isPending}
                >
                  Delete Selected
                </Button>
              </div>
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setSelectedDiamonds(new Set())}
            >
              <X className="h-4 w-4" />
              Clear Selection
            </Button>
          </div>
        </Card>
      )}

      {/* Professional Diamond Filters */}
      <Card>
        <div className="space-y-6">
          {/* Header with toggle */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Search className="h-5 w-5 text-gray-500" />
              <h3 className="text-lg font-semibold text-gray-900">Diamond Search & Filters</h3>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              {showAdvancedFilters ? 'Hide Advanced' : 'Show Advanced'}
            </Button>
          </div>

          {/* Basic Search and Quick Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="md:col-span-2">
              <Input
                label="Search"
                placeholder="Search by certificate, notes, or any field..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <Select
              label="Shape"
              options={isLoadingShapes ? [] : shapeOptions}
              value={shapeFilter}
              onChange={(e) => setShapeFilter(e.target.value)}
              placeholder="All shapes"
            />
            <Select
              label="Status"
              options={DIAMOND_STATUSES}
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              placeholder="All statuses"
            />
          </div>

          {/* 4Cs Filters */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="text-md font-semibold text-gray-900 mb-3">4Cs Grading Filters</h4>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Select
                label="Color Grade"
                options={DIAMOND_COLORS}
                value={colorFilter}
                onChange={(e) => setColorFilter(e.target.value)}
                placeholder="All colors"
              />
              <Select
                label="Clarity Grade"
                options={DIAMOND_CLARITIES}
                value={clarityFilter}
                onChange={(e) => setClarityFilter(e.target.value)}
                placeholder="All clarities"
              />
              <Select
                label="Cut Grade"
                options={CUT_GRADES}
                value={cutGradeFilter}
                onChange={(e) => setCutGradeFilter(e.target.value)}
                placeholder="All cut grades"
              />
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Carat Range</label>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="Min"
                    value={caratMin}
                    onChange={e => setCaratMin(e.target.value)}
                  />
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="Max"
                    value={caratMax}
                    onChange={e => setCaratMax(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="space-y-4">
              {/* Additional Grading */}
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="text-md font-semibold text-gray-900 mb-3">Additional Grading</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <Select
                    label="Polish"
                    options={POLISH_SYMMETRY_GRADES}
                    value={polishFilter}
                    onChange={(e) => setPolishFilter(e.target.value)}
                    placeholder="All polish grades"
                  />
                  <Select
                    label="Symmetry"
                    options={POLISH_SYMMETRY_GRADES}
                    value={symmetryFilter}
                    onChange={(e) => setSymmetryFilter(e.target.value)}
                    placeholder="All symmetry grades"
                  />
                  <Select
                    label="Fluorescence"
                    options={FLUORESCENCE_INTENSITIES}
                    value={fluorescenceFilter}
                    onChange={(e) => setFluorescenceFilter(e.target.value)}
                    placeholder="All fluorescence"
                  />
                </div>
              </div>

              {/* Certification */}
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="text-md font-semibold text-gray-900 mb-3">Certification</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Select
                    label="Certification Lab"
                    options={CERTIFICATION_LABS}
                    value={certificationLabFilter}
                    onChange={(e) => setCertificationLabFilter(e.target.value)}
                    placeholder="All labs"
                  />
                  <Input
                    label="Certificate Number"
                    placeholder="Search by certificate number"
                    value={certificateNoFilter}
                    onChange={(e) => setCertificateNoFilter(e.target.value)}
                  />
                </div>
              </div>

              {/* Pricing */}
              <div className="bg-red-50 p-4 rounded-lg">
                <h4 className="text-md font-semibold text-gray-900 mb-3">Pricing</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Retail Price Range</label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Min Price"
                        value={priceMin}
                        onChange={e => setPriceMin(e.target.value)}
                      />
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Max Price"
                        value={priceMax}
                        onChange={e => setPriceMax(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Cost Price Range</label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Min Cost"
                        value={costPriceMin}
                        onChange={e => setCostPriceMin(e.target.value)}
                      />
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Max Cost"
                        value={costPriceMax}
                        onChange={e => setCostPriceMax(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Inventory */}
              <div className="bg-indigo-50 p-4 rounded-lg">
                <h4 className="text-md font-semibold text-gray-900 mb-3">Inventory</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <Input
                    label="Minimum Quantity"
                    type="number"
                    placeholder="Min quantity"
                    value={quantityMin}
                    onChange={e => setQuantityMin(e.target.value)}
                  />
                  <Input
                    label="Location"
                    placeholder="Storage location"
                    value={locationFilter}
                    onChange={(e) => setLocationFilter(e.target.value)}
                  />
                  <Select
                    label="Vendor"
                    options={vendorOptions || []}
                    value={vendorFilter}
                    onChange={e => setVendorFilter(e.target.value)}
                    placeholder="All vendors"
                  />
                </div>
                <div className="mt-3">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={lowStockOnly}
                      onChange={(e) => setLowStockOnly(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Show only low stock items</span>
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* Filter Actions */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              {totalDiamonds > 0 && `Showing ${filteredDiamonds.length} of ${totalDiamonds} diamonds`}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  // Clear all filters
                  setSearchTerm('');
                  setShapeFilter('');
                  setStatusFilter('');
                  setColorFilter('');
                  setClarityFilter('');
                  setCutGradeFilter('');
                  setCaratMin('');
                  setCaratMax('');
                  setPolishFilter('');
                  setSymmetryFilter('');
                  setFluorescenceFilter('');
                  setCertificationLabFilter('');
                  setCertificateNoFilter('');
                  setPriceMin('');
                  setPriceMax('');
                  setCostPriceMin('');
                  setCostPriceMax('');
                  setQuantityMin('');
                  setLocationFilter('');
                  setVendorFilter('');
                  setLowStockOnly(false);
                }}
              >
                <X className="h-4 w-4 mr-1" />
                Clear All Filters
              </Button>
            </div>
          </div>
        </div>
      </Card>

      {/* Diamond List */}
      {filteredDiamonds.length === 0 ? (
        <EmptyState
          title="No diamonds found"
          description={
            searchTerm || shapeFilter || statusFilter
              ? "No diamonds match your search criteria. Try adjusting your filters."
              : "You haven't added any diamonds yet. Start by adding your first diamond to the inventory."
          }
          action={{
            label: "Add Diamond",
            onClick: () => setIsAddModalOpen(true)
          }}
        />
      ) : viewMode === 'table' ? (
        <Card padding={false}>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      checked={selectedDiamonds.size > 0 && selectedDiamonds.size === filteredDiamonds.length}
                      onChange={(e) => {
                        if (e.target.checked) {
                          // Select all diamonds
                          const allIds = new Set(filteredDiamonds.map(d => d.id));
                          setSelectedDiamonds(allIds);
                        } else {
                          // Deselect all diamonds
                          setSelectedDiamonds(new Set());
                        }
                      }}
                    />
                  </th>
                  {visibleColumns.shape && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => { setSortBy('shape'); setSortDir(sortBy === 'shape' && sortDir === 'asc' ? 'desc' : 'asc'); }}>
                      Shape & Size {sortBy === 'shape' && (sortDir === 'asc' ? '▲' : '▼')}
                    </th>
                  )}
                  {visibleColumns.carat && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => { setSortBy('carat'); setSortDir(sortBy === 'carat' && sortDir === 'asc' ? 'desc' : 'asc'); }}>
                      Carat & Clarity {sortBy === 'carat' && (sortDir === 'asc' ? '▲' : '▼')}
                    </th>
                  )}
                  {visibleColumns.color && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => { setSortBy('color'); setSortDir(sortBy === 'color' && sortDir === 'asc' ? 'desc' : 'asc'); }}>
                      Color & Certificate {sortBy === 'color' && (sortDir === 'asc' ? '▲' : '▼')}
                    </th>
                  )}
                  {visibleColumns.quantity && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => { setSortBy('quantity'); setSortDir(sortBy === 'quantity' && sortDir === 'asc' ? 'desc' : 'asc'); }}>
                      Quantity {sortBy === 'quantity' && (sortDir === 'asc' ? '▲' : '▼')}
                    </th>
                  )}
                  {visibleColumns.price && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => { setSortBy('retail_price'); setSortDir(sortBy === 'retail_price' && sortDir === 'asc' ? 'desc' : 'asc'); }}>
                      Price {sortBy === 'retail_price' && (sortDir === 'asc' ? '▲' : '▼')}
                    </th>
                  )}
                  {visibleColumns.status && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => { setSortBy('status'); setSortDir(sortBy === 'status' && sortDir === 'asc' ? 'desc' : 'asc'); }}>
                      Status {sortBy === 'status' && (sortDir === 'asc' ? '▲' : '▼')}
                    </th>
                  )}
                  {visibleColumns.vendor && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Vendor
                    </th>
                  )}
                  {visibleColumns.purchase_date && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => { setSortBy('purchase_date'); setSortDir(sortBy === 'purchase_date' && sortDir === 'asc' ? 'desc' : 'asc'); }}>
                      Purchase Date {sortBy === 'purchase_date' && (sortDir === 'asc' ? '▲' : '▼')}
                    </th>
                  )}
                  {visibleColumns.actions && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {renderTableRows(filteredDiamonds)}
              </tbody>
            </table>
          </div>
          {/* Pagination Controls */}
          <div className="flex items-center justify-between px-6 py-4 border-t bg-gray-50">
            <div className="text-sm text-gray-600">
              Showing {filteredDiamonds.length > 0 ? ((page - 1) * pageSize + 1) : 0}
              -{(page - 1) * pageSize + filteredDiamonds.length} of {totalDiamonds} diamonds
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setPage((p) => Math.max(1, p - 1))}
                disabled={page === 1}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {page} of {totalPages}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                disabled={page === totalPages || totalDiamonds === 0}
              >
                Next
              </Button>
              <Select
                label="Page Size"
                options={[10, 20, 50, 100].map((n) => ({ value: n.toString(), label: `${n} / page` }))}
                value={pageSize.toString()}
                onChange={(e) => {
                  setPageSize(Number(e.target.value));
                  setPage(1);
                }}
                className="w-28"
              />
            </div>
          </div>
        </Card>
      ) : (
        renderCardView(filteredDiamonds)
      )}

      {/* Modals */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add New Diamond"
        size="lg"
      >
        <DiamondForm
          onSuccess={() => {
            setIsAddModalOpen(false);
            queryClient.invalidateQueries({ queryKey: ['diamonds'] });
          }}
        />
      </Modal>

      <Modal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedDiamond(null);
        }}
        title="Edit Diamond"
        size="lg"
      >
        {selectedDiamond && (
          <DiamondForm
            diamond={selectedDiamond}
            onSuccess={() => {
              setIsEditModalOpen(false);
              setSelectedDiamond(null);
              queryClient.invalidateQueries({ queryKey: ['diamonds'] });
            }}
          />
        )}
      </Modal>

      <Modal
        isOpen={isDeductModalOpen}
        onClose={() => {
          setIsDeductModalOpen(false);
          setSelectedDiamond(null);
        }}
        title="Deduct Stock"
        size="md"
      >
        {selectedDiamond && (
          <DeductStockForm
            diamond={selectedDiamond}
            onSuccess={() => {
              setIsDeductModalOpen(false);
              setSelectedDiamond(null);
              queryClient.invalidateQueries({ queryKey: ['diamonds'] });
            }}
          />
        )}
      </Modal>

      {/* Deduct Confirmation Modal */}
      <Modal
        isOpen={isDeductConfirmOpen}
        onClose={cancelDeduct}
        title="Confirm Deduct Stock"
        size="sm"
      >
        <div className="space-y-4">
          <p>Are you sure you want to deduct stock for this diamond?</p>
          <div className="flex justify-end space-x-2">
            <Button variant="secondary" onClick={cancelDeduct}>Cancel</Button>
            <Button variant="danger" onClick={confirmDeduct}>Yes, Deduct</Button>
          </div>
        </div>
      </Modal>

      <Modal
        isOpen={isAssignModalOpen}
        onClose={() => {
          setIsAssignModalOpen(false);
          setSelectedDiamond(null);
          setSelectedManufacturing(null);
          setSelectedJewelry(null);
        }}
        title={`Assign Diamond: ${selectedDiamond?.shape} ${selectedDiamond?.carat}ct`}
        size="md"
      >
        {selectedDiamond && (
          <div className="space-y-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">Diamond Details</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>Shape: {selectedDiamond.shape}</div>
                <div>Carat: {selectedDiamond.carat}</div>
                <div>Color: {selectedDiamond.color}</div>
                <div>Clarity: {selectedDiamond.clarity}</div>
                <div>Quantity: {selectedDiamond.quantity}</div>
                <div>Status: {selectedDiamond.status}</div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900 mb-3">Assign to Manufacturing</h3>
                <Select
                  label="Select Manufacturing"
                  options={manufacturingOptions || []}
                  value={selectedManufacturing || ''}
                  onChange={(e) => setSelectedManufacturing(Number(e.target.value) || null)}
                  placeholder="Choose manufacturing..."
                />
                <Button 
                  onClick={() => assignManufacturingMutation.mutate()} 
                  isLoading={assignManufacturingMutation.isPending}
                  disabled={!selectedManufacturing}
                  className="mt-2 w-full"
                >
                  Assign to Manufacturing
                </Button>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-medium text-gray-900 mb-3">Assign to Jewelry</h3>
                <Select
                  label="Select Jewelry"
                  options={jewelryOptionsMapped || []}
                  value={selectedJewelry || ''}
                  onChange={(e) => setSelectedJewelry(Number(e.target.value) || null)}
                  placeholder="Choose jewelry..."
                />
                <Button 
                  onClick={() => assignJewelryMutation.mutate()} 
                  isLoading={assignJewelryMutation.isPending}
                  disabled={!selectedJewelry}
                  className="mt-2 w-full"
                >
                  Assign to Jewelry
                </Button>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* Bulk Edit Modal */}
      {isBulkEditOpen && (
        <Modal
          isOpen={isBulkEditOpen}
          onClose={() => {
            setIsBulkEditOpen(false);
            setBulkEditData({});
          }}
          title={`Bulk Edit ${selectedDiamonds.size} Diamonds`}
          size="lg"
        >
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Only fill in the fields you want to update. Empty fields will remain unchanged.
            </p>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={bulkEditData.status || ''}
                  onChange={(e) => setBulkEditData(prev => ({ ...prev, status: e.target.value || undefined }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Keep current</option>
                  <option value="in_stock">In Stock</option>
                  <option value="reserved">Reserved</option>
                  <option value="sold">Sold</option>
                  <option value="manufacturing">Manufacturing</option>
                  <option value="damaged">Damaged</option>
                  <option value="lost">Lost</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Location
                </label>
                <input
                  type="text"
                  value={bulkEditData.location || ''}
                  onChange={(e) => setBulkEditData(prev => ({ ...prev, location: e.target.value || undefined }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Keep current"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Stock
                </label>
                <input
                  type="number"
                  min="0"
                  value={bulkEditData.minimum_stock || ''}
                  onChange={(e) => setBulkEditData(prev => ({ ...prev, minimum_stock: e.target.value ? parseInt(e.target.value) : undefined }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Keep current"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Retail Price ($)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={bulkEditData.retail_price || ''}
                  onChange={(e) => setBulkEditData(prev => ({ ...prev, retail_price: e.target.value ? parseFloat(e.target.value) : undefined }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Keep current"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notes
              </label>
              <textarea
                value={bulkEditData.notes || ''}
                onChange={(e) => setBulkEditData(prev => ({ ...prev, notes: e.target.value || undefined }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                placeholder="Keep current"
              />
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="secondary"
                onClick={() => {
                  setIsBulkEditOpen(false);
                  setBulkEditData({});
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  // Filter out undefined values
                  const updates = Object.fromEntries(
                    Object.entries(bulkEditData).filter(([_, value]) => value !== undefined && value !== '')
                  );

                  if (Object.keys(updates).length === 0) {
                    toast.error('Please select at least one field to update');
                    return;
                  }

                  bulkUpdateMutation.mutate({
                    diamond_ids: Array.from(selectedDiamonds),
                    updates
                  });
                }}
                disabled={bulkUpdateMutation.isPending}
                isLoading={bulkUpdateMutation.isPending}
              >
                Update Diamonds
              </Button>
            </div>
          </div>
        </Modal>
      )}

      {/* Bulk Status Update Modal */}
      {isBulkStatusOpen && (
        <Modal
          isOpen={isBulkStatusOpen}
          onClose={() => setIsBulkStatusOpen(false)}
          title={`Update Status for ${selectedDiamonds.size} Diamonds`}
          size="sm"
        >
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                New Status
              </label>
              <select
                value={bulkEditData.status || ''}
                onChange={(e) => setBulkEditData(prev => ({ ...prev, status: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select status</option>
                <option value="in_stock">In Stock</option>
                <option value="reserved">Reserved</option>
                <option value="sold">Sold</option>
                <option value="manufacturing">Manufacturing</option>
                <option value="damaged">Damaged</option>
                <option value="lost">Lost</option>
              </select>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="secondary"
                onClick={() => setIsBulkStatusOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  if (!bulkEditData.status) {
                    toast.error('Please select a status');
                    return;
                  }

                  bulkUpdateMutation.mutate({
                    diamond_ids: Array.from(selectedDiamonds),
                    updates: { status: bulkEditData.status }
                  });
                }}
                disabled={bulkUpdateMutation.isPending}
                isLoading={bulkUpdateMutation.isPending}
              >
                Update Status
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default DiamondList;