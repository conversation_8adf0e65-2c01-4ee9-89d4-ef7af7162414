import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Diamond,
  Gem,
  ShoppingCart,
  Wrench,
  TrendingUp,
  Package,
  DollarSign,
  AlertTriangle,
  BarChart3
} from 'lucide-react';
import { api } from '../lib/api';
import { DashboardSummary, SalesStats, StockLevels, DashboardActivity } from '../types';
import Card from '../components/ui/Card';
import SimpleChart from '../components/charts/SimpleChart';
import { LoadingState, ErrorState } from '../components/DataStates';

const Dashboard: React.FC = () => {
  const {
    data: summary,
    isLoading: summaryLoading,
    error: summaryError,
    refetch: refetchSummary
  } = useQuery({
    queryKey: ['dashboard-summary'],
    queryFn: async (): Promise<DashboardSummary> => {
      const response = await api.dashboard.summary();
      if (!response) {
        throw new Error('Failed to fetch dashboard summary');
      }
      return response;
    },
    retry: 1
  });

  const { 
    data: salesStats, 
    isLoading: salesLoading, 
    error: salesError,
    refetch: refetchSales 
  } = useQuery({
    queryKey: ['dashboard-sales'],
    queryFn: async (): Promise<SalesStats> => {
      const response = await api.dashboard.sales();
      if (!response) {
        throw new Error('Failed to fetch sales stats');
      }
      return response;
    },
    retry: 1
  });

  const { 
    data: stockLevels, 
    isLoading: stockLoading, 
    error: stockError,
    refetch: refetchStock 
  } = useQuery({
    queryKey: ['dashboard-stock'],
    queryFn: async (): Promise<StockLevels> => {
      const response = await api.dashboard.stock();
      if (!response) {
        throw new Error('Failed to fetch stock levels');
      }
      return response;
    },
    retry: 1
  });

  const {
    data: activity,
    isLoading: activityLoading,
    error: activityError,
    refetch: refetchActivity
  } = useQuery({
    queryKey: ['dashboard-activity'],
    queryFn: async (): Promise<DashboardActivity[]> => {
      try {
        const response = await api.dashboard.activity();
        if (!response) {
          throw new Error('Failed to fetch recent activity');
        }
        return Array.isArray(response) ? response : [];
      } catch (error) {
        console.warn('Activity endpoint not available, returning empty array');
        return [];
      }
    },
    retry: 1
  });

  const {
    data: analytics,
    isLoading: analyticsLoading,
    error: analyticsError
  } = useQuery({
    queryKey: ['dashboard-analytics'],
    queryFn: async () => {
      const response = await api.dashboard.analytics();
      if (!response) {
        throw new Error('Failed to fetch dashboard analytics');
      }
      return response;
    },
    retry: 1
  });

  const {
    data: alerts,
    isLoading: alertsLoading,
    error: alertsError,
    refetch: refetchAlerts
  } = useQuery({
    queryKey: ['dashboard-alerts'],
    queryFn: async () => {
      try {
        const response = await api.dashboard.alerts();
        return Array.isArray(response) ? response : [];
      } catch (error) {
        console.warn('Alerts endpoint not available, returning empty array');
        return [];
      }
    },
    retry: false, // Don't retry failed requests
    refetchOnWindowFocus: false, // Don't refetch on window focus
    staleTime: 5 * 60 * 1000, // Consider data stale after 5 minutes
  });

  const isLoading = summaryLoading || salesLoading || stockLoading || activityLoading || analyticsLoading;
  const hasError = summaryError || salesError || stockError || activityError || analyticsError;

  // Enhanced error handling for API calls
  const handleRetry = () => {
    refetchSummary();
    refetchSales();
    refetchStock();
    refetchActivity();
  };

  if (isLoading) {
    return <div className="spinner">Loading dashboard...</div>;
  }

  if (hasError) {
    const errorDetails = {
      summary: summaryError?.message || 'Summary data unavailable',
      sales: salesError?.message || 'Sales data unavailable',
      stock: stockError?.message || 'Stock data unavailable',
    };

    return (
      <ErrorState
        title="Failed to load dashboard"
        description={`Unable to load dashboard data. Details: ${JSON.stringify(errorDetails)}`}
        onRetry={handleRetry}
        error={(summaryError || salesError || stockError) as Error | undefined}
      />
    );
  }

  // Refactored reusable card rendering logic
  const renderCards = (cards: Array<{
    title: string;
    value: number | string;
    icon: React.ElementType;
    color: string;
    change?: string;
    subtitle?: string;
    priority?: boolean;
  }>) => (
    cards.map((card, index) => (
      <Card key={`${card.title}-${index}`} className="relative overflow-hidden">
        <div className="flex items-center">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600">{card.title}</p>
            <p className={`text-2xl font-bold ${card.priority ? 'text-purple-900' : 'text-gray-900'}`}>
              {typeof card.value === 'string' ? card.value : card.value.toLocaleString()}
            </p>
            {card.subtitle && (
              <p className="text-sm text-gray-500 mt-1">
                {card.subtitle}
              </p>
            )}
            {card.change && (
              <p className={`text-sm font-medium ${
                card.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
              }`}>
                {card.change} from last month
              </p>
            )}
          </div>
          <div className={`p-3 rounded-full ${card.color}`}>
            {card.icon ? (
              <card.icon className="h-6 w-6 text-white" />
            ) : (
              <div className="h-6 w-6 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-xs text-gray-500">No Icon</span>
              </div>
            )}
          </div>
        </div>
      </Card>
    ))
  );

  // TOP PRIORITY: Carat Weight Cards
  const caratWeightCards = [
    {
      title: 'Total Carat Weight',
      value: `${summary?.total_carat_weight?.toFixed(2) || '0.00'}ct`,
      subtitle: `Avg: ${summary?.average_carat_per_diamond?.toFixed(3) || '0.000'}ct per diamond`,
      icon: Diamond,
      color: 'bg-purple-600',
      priority: true
    },
    {
      title: 'Available Carat Weight',
      value: `${summary?.available_carat_weight?.toFixed(2) || '0.00'}ct`,
      subtitle: `${summary?.diamonds_in_stock || 0} diamonds available`,
      icon: Gem,
      color: 'bg-green-600',
      priority: true
    },
    {
      title: 'Reserved Carat Weight',
      value: `${summary?.reserved_carat_weight?.toFixed(2) || '0.00'}ct`,
      subtitle: `${summary?.diamonds_reserved || 0} diamonds reserved`,
      icon: Package,
      color: 'bg-orange-600',
      priority: true
    }
  ];

  const summaryCards = [
    {
      title: 'Diamonds Available',
      value: summary?.diamonds_in_stock || 0,
      subtitle: `Total: ${summary?.total_diamonds || 0}`,
      icon: Diamond,
      color: 'bg-blue-500',
    },
    {
      title: 'Diamonds Reserved',
      value: summary?.diamonds_reserved || 0,
      subtitle: `Manufacturing: ${summary?.diamonds_manufacturing || 0}`,
      icon: Package,
      color: 'bg-yellow-500',
    },
    {
      title: 'Jewelry in Stock',
      value: summary?.jewelry_in_stock || 0,
      subtitle: `Sold: ${summary?.jewelry_sold || 0}`,
      icon: Gem,
      color: 'bg-emerald-500',
    },
    {
      title: 'Total Sales',
      value: summary?.total_sales || 0,
      subtitle: `Manufacturing: ${summary?.open_manufacturing || 0}`,
      icon: ShoppingCart,
      color: 'bg-amber-500',
    }
  ];

  const salesCards = [
    {
      title: 'Total Sales',
      value: salesStats?.total_sales || 0,
      icon: TrendingUp,
      color: 'bg-green-500'
    },
    {
      title: 'Paid Sales',
      value: salesStats?.paid_sales || 0,
      icon: DollarSign,
      color: 'bg-blue-500'
    },
    {
      title: 'Unpaid Sales',
      value: salesStats?.unpaid_sales || 0,
      icon: Package,
      color: 'bg-red-500'
    }
  ];



  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome to your inventory management system</p>
      </div>

      {/* TOP PRIORITY: Carat Weight Cards */}
      <div className="mb-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {renderCards(caratWeightCards)}
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {renderCards(summaryCards)}
      </div>

      {/* Sales Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Sales Overview</h3>
          <div className="space-y-4">
            {renderCards(salesCards)}
          </div>
        </Card>

        <Card>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Stock Summary</h3>
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Diamond className="h-5 w-5 text-blue-600 mr-2" />
                <div>
                  <span className="text-sm font-medium text-gray-700">Diamonds Available</span>
                  <p className="text-xs text-gray-500">Total: {summary?.total_diamonds || 0}</p>
                </div>
              </div>
              <span className="text-lg font-bold text-gray-900">
                {summary?.diamonds_in_stock || 0}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Package className="h-5 w-5 text-yellow-600 mr-2" />
                <div>
                  <span className="text-sm font-medium text-gray-700">Diamonds Reserved</span>
                  <p className="text-xs text-gray-500">Manufacturing: {summary?.diamonds_manufacturing || 0}</p>
                </div>
              </div>
              <span className="text-lg font-bold text-gray-900">
                {summary?.diamonds_reserved || 0}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Gem className="h-5 w-5 text-emerald-600 mr-2" />
                <div>
                  <span className="text-sm font-medium text-gray-700">Jewelry Items</span>
                  <p className="text-xs text-gray-500">Sold: {summary?.jewelry_sold || 0}</p>
                </div>
              </div>
              <span className="text-lg font-bold text-gray-900">
                {stockLevels?.jewelry_in_stock || 0}
              </span>
            </div>
            <div className="pt-4 border-t border-gray-200">
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <p className="text-sm text-gray-600">Total Carat Weight</p>
                  <p className="text-xl font-bold text-purple-900">
                    {summary?.total_carat_weight?.toFixed(2) || '0.00'}ct
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Inventory Value</p>
                  <p className="text-xl font-bold text-gray-900">
                    {summary?.total_inventory_value?.toLocaleString('en-IN', { style: 'currency', currency: 'INR' }) ?? '₹0'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Analytics Charts */}
      {analytics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Status Distribution */}
          {analytics.distributions?.status && analytics.distributions.status.length > 0 && (
            <SimpleChart
              type="doughnut"
              title="Diamond Status Distribution"
              data={analytics.distributions.status.map((item: any) => ({
                label: item.status.replace('_', ' ').toUpperCase(),
                value: item.count
              }))}
            />
          )}

          {/* Shape Distribution */}
          {analytics.distributions?.shape && analytics.distributions.shape.length > 0 && (
            <SimpleChart
              type="bar"
              title="Diamond Shape Distribution"
              data={analytics.distributions.shape.map((item: any) => ({
                label: item.shape,
                value: item.count
              }))}
            />
          )}

          {/* Color Distribution */}
          {analytics.distributions?.color && analytics.distributions.color.length > 0 && (
            <SimpleChart
              type="pie"
              title="Diamond Color Distribution"
              data={analytics.distributions.color.map((item: any) => ({
                label: item.color,
                value: item.count
              }))}
            />
          )}

          {/* Overview Stats */}
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              <BarChart3 className="h-5 w-5 inline mr-2" />
              Analytics Overview
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Total Diamonds:</span>
                <span className="font-semibold">{analytics.overview?.total_diamonds || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Total Value:</span>
                <span className="font-semibold">
                  ${(analytics.overview?.total_value || 0).toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  })}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Average Price:</span>
                <span className="font-semibold">
                  ${(analytics.overview?.average_price || 0).toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  })}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Recent Additions (30d):</span>
                <span className="font-semibold">{analytics.overview?.recent_additions || 0}</span>
              </div>
              {analytics.overview?.low_stock_count > 0 && (
                <div className="flex justify-between items-center p-2 bg-yellow-50 rounded">
                  <span className="text-yellow-700 flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    Low Stock Items:
                  </span>
                  <span className="font-semibold text-yellow-800">
                    {analytics.overview.low_stock_count}
                  </span>
                </div>
              )}
            </div>
          </Card>
        </div>
      )}

      {/* Diamond Stock Alerts */}
      {alerts && alerts.length > 0 && (
        <Card>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
            Diamond Stock Alerts
          </h3>
          <div className="space-y-3">
            {alerts.map((alert, idx) => (
              <div key={`alert-${alert.type}-${idx}`} className={`p-3 rounded-lg border-l-4 ${
                alert.severity === 'critical'
                  ? 'bg-red-50 border-red-400'
                  : 'bg-yellow-50 border-yellow-400'
              }`}>
                <div className="flex items-start">
                  <div className="flex-1">
                    <p className={`text-sm font-medium ${
                      alert.severity === 'critical' ? 'text-red-800' : 'text-yellow-800'
                    }`}>
                      {alert.message}
                    </p>
                    <p className={`text-xs mt-1 ${
                      alert.severity === 'critical' ? 'text-red-600' : 'text-yellow-600'
                    }`}>
                      {alert.diamond_info}
                    </p>
                  </div>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    alert.severity === 'critical'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {alert.severity}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Recent Activity */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-2">
          {activity?.map((item, idx) => (
            <div key={`activity-${item.type}-${idx}`} className="text-sm text-gray-700">
              <strong>{item.type}:</strong> {item.description} — <em>{new Date(item.date).toLocaleString()}</em>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default Dashboard;