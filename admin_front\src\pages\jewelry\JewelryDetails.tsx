import React from 'react';
import { Jewelry } from '../../types';
import Card from '../../components/ui/Card';

interface JewelryDetailsProps {
  jewelry: Jewelry;
}

const JewelryDetails: React.FC<JewelryDetailsProps> = ({ jewelry }) => {
  return (
    <div className="space-y-6">
      {/* Image */}
      {jewelry.image_path && (
        <div className="text-center">
          <img
            src={`${(import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api').replace('/api', '')}${jewelry.image_path}`}
            alt={jewelry.name}
            className="max-w-full h-64 object-cover rounded-lg mx-auto"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
            }}
          />
        </div>
      )}

      {/* Basic Information */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="text-sm text-gray-500">Name:</span>
            <p className="font-medium">{jewelry.name}</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Design Code:</span>
            <p className="font-medium">{jewelry.design_code}</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Vendor:</span>
            <p className="font-medium">{jewelry.vendor?.name || 'N/A'}</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Status:</span>
            <p className={`font-medium ${
              jewelry.status === 'in_stock' ? 'text-green-600' : 'text-red-600'
            }`}>
              {jewelry.status === 'in_stock' ? 'In Stock' : 'Sold'}
            </p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Gross Weight:</span>
            <p className="font-medium">{jewelry.gross_weight}g</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Created:</span>
            <p className="font-medium">{jewelry.created_at ? new Date(jewelry.created_at).toLocaleDateString() : 'N/A'}</p>
          </div>
        </div>
      </Card>

      {/* Diamonds Used */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Diamonds Used</h3>
        {jewelry.diamonds && jewelry.diamonds.length > 0 ? (
          <div className="space-y-4">
            {jewelry.diamonds.map((item, idx) => (
              <div key={`diamond-${item.diamond_id || idx}`} className="border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div>
                    <span className="text-sm text-gray-500">Shape & Weight:</span>
                    <p className="font-medium">
                      {item.diamond?.shape || 'Unknown Shape'} - {item.diamond?.carat || 0}ct
                    </p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Color & Clarity:</span>
                    <p className="font-medium">
                      {item.diamond?.color || 'N/A'} / {item.diamond?.clarity || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Quantity Used:</span>
                    <p className="font-medium text-blue-600">{item.quantity} pcs</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Certificate:</span>
                    <p className="font-medium">{item.diamond?.certificate_no || 'No Certificate'}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Cut Grade:</span>
                    <p className="font-medium">{item.diamond?.cut_grade || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Status:</span>
                    <p className={`font-medium ${
                      item.diamond?.status === 'in_stock' ? 'text-green-600' :
                      item.diamond?.status === 'reserved' ? 'text-yellow-600' :
                      item.diamond?.status === 'sold' ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {item.diamond?.status?.replace('_', ' ').toUpperCase() || 'Unknown'}
                    </p>
                  </div>
                </div>
                {item.diamond?.cost_price && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <span className="text-sm text-gray-500">Cost Price:</span>
                        <p className="font-medium">₹{item.diamond.cost_price.toLocaleString()}</p>
                      </div>
                      {item.diamond.retail_price && (
                        <div>
                          <span className="text-sm text-gray-500">Retail Price:</span>
                          <p className="font-medium">₹{item.diamond.retail_price.toLocaleString()}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="font-medium text-gray-700">Total Diamonds Used:</span>
                <span className="font-bold text-blue-600">
                  {jewelry.diamonds.reduce((total, item) => total + (item.quantity || 0), 0)} pieces
                </span>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <p>No diamonds associated with this jewelry item.</p>
          </div>
        )}
      </Card>
    </div>
  );
};

export default JewelryDetails;