import React from 'react';
import { Jewelry } from '../../types';
import Card from '../../components/ui/Card';

interface JewelryDetailsProps {
  jewelry: Jewelry;
}

const JewelryDetails: React.FC<JewelryDetailsProps> = ({ jewelry }) => {
  return (
    <div className="space-y-6">
      {/* Image */}
      {jewelry.image_path && (
        <div className="text-center">
          <img
            src={`${(import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api').replace('/api', '')}${jewelry.image_path}`}
            alt={jewelry.name}
            className="max-w-full h-64 object-cover rounded-lg mx-auto"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
            }}
          />
        </div>
      )}

      {/* Basic Information */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="text-sm text-gray-500">Name:</span>
            <p className="font-medium">{jewelry.name}</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Design Code:</span>
            <p className="font-medium">{jewelry.design_code}</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Vendor:</span>
            <p className="font-medium">{jewelry.vendor?.name || 'N/A'}</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Status:</span>
            <p className={`font-medium ${
              jewelry.status === 'in_stock' ? 'text-green-600' : 'text-red-600'
            }`}>
              {jewelry.status === 'in_stock' ? 'In Stock' : 'Sold'}
            </p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Gross Weight:</span>
            <p className="font-medium">{jewelry.gross_weight}g</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Created:</span>
            <p className="font-medium">{jewelry.created_at ? new Date(jewelry.created_at).toLocaleDateString() : 'N/A'}</p>
          </div>
        </div>
      </Card>

      {/* Diamonds Used */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Diamonds Used</h3>
        <div className="space-y-2">
          {jewelry.diamonds?.map((item, idx) => (
            <div key={idx} className="text-sm text-gray-700">
              <strong>{item.diamond?.shape || 'Unknown Shape'}:</strong> {item.diamond?.certificate_no || 'No Certificate'} — {item.quantity} pcs
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default JewelryDetails;