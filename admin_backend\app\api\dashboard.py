from flask_restx import Namespace, Resource, fields
from app.models.diamond import Diamond
from app.models.jewelry import JewelryItem
from app.models.sale import Sale
from app.models.manufacturing import ManufacturingRequest
from app.utils.decorators import token_required
from app import db
from sqlalchemy import func

# Define namespace

dashboard_ns = Namespace('dashboard', description='Dashboard statistics and summaries', path='/dashboard')

summary_model = dashboard_ns.model('DashboardSummary', {
    'diamonds_in_stock': fields.Integer,
    'total_diamonds': fields.Integer,
    'diamonds_sold': fields.Integer,
    'diamonds_manufacturing': fields.Integer,
    'diamonds_reserved': fields.Integer,
    'total_carat_weight': fields.Float,
    'available_carat_weight': fields.Float,
    'reserved_carat_weight': fields.Float,
    'average_carat_per_diamond': fields.Float,
    'jewelry_in_stock': fields.Integer,
    'jewelry_sold': fields.Integer,
    'total_sales': fields.Integer,
    'open_manufacturing': fields.Integer,
    'completed_manufacturing': fields.Integer,
    'total_inventory_value': fields.Float
})

sales_stats_model = dashboard_ns.model('SalesStats', {
    'total_sales': fields.Integer,
    'paid_sales': fields.Integer,
    'unpaid_sales': fields.Integer
})

stock_stats_model = dashboard_ns.model('StockStats', {
    'diamonds_in_stock': fields.Integer,
    'jewelry_in_stock': fields.Integer
})

error_model = dashboard_ns.model('DashboardError', {
    'status': fields.String(description='Error status'),
    'message': fields.String(description='Error message'),
    'status_code': fields.Integer(description='HTTP status code')
})

@dashboard_ns.route('/')
class Dashboard(Resource):
    @dashboard_ns.doc('dashboard_main')
    @dashboard_ns.marshal_with(summary_model)
    @dashboard_ns.response(200, 'Dashboard data', summary_model)
    @dashboard_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """Get main dashboard data"""
        try:
            # Fix diamond counting - use SUM of available quantities instead of COUNT
            diamonds_in_stock = db.session.query(
                func.coalesce(func.sum(Diamond.available_quantity), 0)
            ).filter(
                Diamond.status.in_(['in_stock', 'reserved'])
            ).scalar() or 0

            # Also get total diamond count (all statuses) - handle NULL values
            total_diamonds = db.session.query(
                func.coalesce(func.sum(Diamond.quantity), 0)
            ).scalar() or 0

            # Get diamonds by status for better tracking
            diamonds_sold = db.session.query(
                func.coalesce(func.sum(Diamond.quantity), 0)
            ).filter_by(status='sold').scalar() or 0

            diamonds_manufacturing = db.session.query(
                func.coalesce(func.sum(Diamond.quantity), 0)
            ).filter_by(status='manufacturing').scalar() or 0

            diamonds_reserved = db.session.query(
                func.coalesce(func.sum(Diamond.reserved_quantity), 0)
            ).scalar() or 0

            # Calculate total carat weights - TOP PRIORITY for diamond stock management
            # Handle NULL carat values by filtering them out
            total_carat_weight = db.session.query(
                func.coalesce(func.sum(Diamond.carat * Diamond.quantity), 0)
            ).filter(Diamond.carat.isnot(None)).scalar() or 0.0

            available_carat_weight = db.session.query(
                func.coalesce(func.sum(Diamond.carat * Diamond.available_quantity), 0)
            ).filter(
                Diamond.status.in_(['in_stock', 'reserved']),
                Diamond.carat.isnot(None)
            ).scalar() or 0.0

            reserved_carat_weight = db.session.query(
                func.coalesce(func.sum(Diamond.carat * Diamond.reserved_quantity), 0)
            ).filter(Diamond.carat.isnot(None)).scalar() or 0.0

            # Calculate average carat per diamond (only for diamonds with carat values)
            diamond_count = db.session.query(Diamond).count()
            diamonds_with_carat = db.session.query(Diamond).filter(
                Diamond.carat.isnot(None)
            ).count()
            average_carat_per_diamond = (
                total_carat_weight / diamonds_with_carat
            ) if diamonds_with_carat > 0 else 0.0

            # Debug logging to identify the issue
            print(f"DEBUG: Total diamonds: {diamond_count}")
            print(f"DEBUG: Diamonds with carat: {diamonds_with_carat}")
            print(f"DEBUG: Total carat weight: {total_carat_weight}")
            print(f"DEBUG: Available carat weight: {available_carat_weight}")

            jewelry_in_stock = JewelryItem.query.filter_by(status='in_stock').count()
            jewelry_sold = JewelryItem.query.filter_by(status='sold').count()
            total_sales = Sale.query.count()
            open_manufacturing = ManufacturingRequest.query.filter_by(status='open').count()
            completed_manufacturing = ManufacturingRequest.query.filter_by(
                status='completed'
            ).count()

            # Calculate total inventory value - use available quantities for accurate valuation
            diamond_value = db.session.query(
                func.sum(Diamond.cost_price * Diamond.available_quantity)
            ).filter(Diamond.status.in_(['in_stock', 'reserved'])).scalar() or 0

            jewelry_value = db.session.query(func.sum(JewelryItem.cost_price)).filter_by(
                status='in_stock'
            ).scalar() or 0
            total_inventory_value = diamond_value + jewelry_value

            return {
                'diamonds_in_stock': int(diamonds_in_stock),
                'total_diamonds': int(total_diamonds),
                'diamonds_sold': int(diamonds_sold),
                'diamonds_manufacturing': int(diamonds_manufacturing),
                'diamonds_reserved': int(diamonds_reserved),
                'total_carat_weight': round(float(total_carat_weight), 2),
                'available_carat_weight': round(float(available_carat_weight), 2),
                'reserved_carat_weight': round(float(reserved_carat_weight), 2),
                'average_carat_per_diamond': round(float(average_carat_per_diamond), 3),
                'jewelry_in_stock': jewelry_in_stock,
                'jewelry_sold': jewelry_sold,
                'total_sales': total_sales,
                'open_manufacturing': open_manufacturing,
                'completed_manufacturing': completed_manufacturing,
                'total_inventory_value': float(total_inventory_value)
            }
        except Exception as e:
            dashboard_ns.abort(500, f'Error retrieving dashboard data: {str(e)}')

@dashboard_ns.route('/summary')
class DashboardSummary(Resource):
    @dashboard_ns.doc('dashboard_summary')
    @dashboard_ns.marshal_with(summary_model)
    @dashboard_ns.response(200, 'Dashboard summary', summary_model)
    @dashboard_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        try:
            # Fix diamond counting - use SUM of available quantities instead of COUNT
            diamonds_in_stock = db.session.query(
                func.coalesce(func.sum(Diamond.available_quantity), 0)
            ).filter(
                Diamond.status.in_(['in_stock', 'reserved'])
            ).scalar() or 0

            # Also get total diamond count and other metrics
            total_diamonds = db.session.query(
                func.coalesce(func.sum(Diamond.quantity), 0)
            ).scalar() or 0

            diamonds_sold = db.session.query(
                func.coalesce(func.sum(Diamond.quantity), 0)
            ).filter_by(status='sold').scalar() or 0

            diamonds_manufacturing = db.session.query(
                func.coalesce(func.sum(Diamond.quantity), 0)
            ).filter_by(status='manufacturing').scalar() or 0

            diamonds_reserved = db.session.query(
                func.coalesce(func.sum(Diamond.reserved_quantity), 0)
            ).scalar() or 0

            # Calculate total carat weights for summary - Handle NULL carat values
            total_carat_weight = db.session.query(
                func.coalesce(func.sum(Diamond.carat * Diamond.quantity), 0)
            ).filter(Diamond.carat.isnot(None)).scalar() or 0.0

            available_carat_weight = db.session.query(
                func.coalesce(func.sum(Diamond.carat * Diamond.available_quantity), 0)
            ).filter(
                Diamond.status.in_(['in_stock', 'reserved']),
                Diamond.carat.isnot(None)
            ).scalar() or 0.0

            reserved_carat_weight = db.session.query(
                func.coalesce(func.sum(Diamond.carat * Diamond.reserved_quantity), 0)
            ).filter(Diamond.carat.isnot(None)).scalar() or 0.0

            # Calculate average carat per diamond (only for diamonds with carat values)
            diamond_count = db.session.query(Diamond).count()
            diamonds_with_carat = db.session.query(Diamond).filter(Diamond.carat.isnot(None)).count()
            average_carat_per_diamond = (total_carat_weight / diamonds_with_carat) if diamonds_with_carat > 0 else 0.0

            jewelry_in_stock = JewelryItem.query.filter_by(status='in_stock').count()
            jewelry_sold = JewelryItem.query.filter_by(status='sold').count()
            total_sales = Sale.query.count()
            open_manufacturing = ManufacturingRequest.query.filter_by(status='open').count()
            completed_manufacturing = ManufacturingRequest.query.filter_by(status='completed').count()

            # Calculate proper inventory value using cost_price * available_quantity
            diamond_value = db.session.query(
                func.sum(Diamond.cost_price * Diamond.available_quantity)
            ).filter(Diamond.status.in_(['in_stock', 'reserved'])).scalar() or 0

            jewelry_value = db.session.query(func.sum(JewelryItem.cost_price)).filter_by(status='in_stock').scalar() or 0
            total_inventory_value = float(diamond_value) + float(jewelry_value)

            return {
                'diamonds_in_stock': int(diamonds_in_stock),
                'total_diamonds': int(total_diamonds),
                'diamonds_sold': int(diamonds_sold),
                'diamonds_manufacturing': int(diamonds_manufacturing),
                'diamonds_reserved': int(diamonds_reserved),
                'total_carat_weight': round(float(total_carat_weight), 2),
                'available_carat_weight': round(float(available_carat_weight), 2),
                'reserved_carat_weight': round(float(reserved_carat_weight), 2),
                'average_carat_per_diamond': round(float(average_carat_per_diamond), 3),
                'jewelry_in_stock': jewelry_in_stock,
                'jewelry_sold': jewelry_sold,
                'total_sales': total_sales,
                'open_manufacturing': open_manufacturing,
                'completed_manufacturing': completed_manufacturing,
                'total_inventory_value': total_inventory_value
            }, 200
        except Exception as e:
            dashboard_ns.abort(500, f'Failed to get dashboard summary: {str(e)}')

# Add recent activity endpoint
activity_model = dashboard_ns.model('DashboardActivity', {
    'type': fields.String,
    'description': fields.String,
    'date': fields.String
})

@dashboard_ns.route('/activity')
class DashboardActivity(Resource):
    @dashboard_ns.doc('dashboard_activity')
    @dashboard_ns.marshal_list_with(activity_model)
    @dashboard_ns.response(200, 'Recent activity', [activity_model])
    @dashboard_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        try:
            all_activity = []

            # Get recent sales with safe access
            try:
                sales = Sale.query.order_by(Sale.sale_date.desc()).limit(5).all()
                for s in sales:
                    if s.sale_date:
                        all_activity.append({
                            'type': 'sale',
                            'description': f"Sale to {s.customer_name or 'Unknown'} (Invoice: {s.invoice_no or 'N/A'})",
                            'date': s.sale_date.isoformat()
                        })
            except Exception:
                pass

            # Get recent manufacturing with safe access
            try:
                manufacturing = ManufacturingRequest.query.order_by(ManufacturingRequest.sent_date.desc()).limit(5).all()
                for m in manufacturing:
                    if m.sent_date:
                        all_activity.append({
                            'type': 'manufacturing',
                            'description': f"Manufacturing request sent to vendor {m.vendor_id or 'Unknown'}",
                            'date': m.sent_date.isoformat()
                        })
            except Exception:
                pass

            # Get recent jewelry added with safe access
            try:
                jewelry = JewelryItem.query.order_by(JewelryItem.received_date.desc()).limit(5).all()
                for j in jewelry:
                    if j.received_date:
                        all_activity.append({
                            'type': 'jewelry',
                            'description': f"Jewelry item {j.name or 'Unknown'} received (Design: {j.design_code or 'N/A'})",
                            'date': j.received_date.isoformat()
                        })
            except Exception:
                pass

            # Get recent diamond activities with safe access
            try:
                diamonds = Diamond.query.order_by(Diamond.created_at.desc()).limit(5).all()
                for d in diamonds:
                    if d.created_at:
                        all_activity.append({
                            'type': 'diamond',
                            'description': f"Diamond added: {d.carat or 0}ct {d.shape.name if d.shape else 'Unknown'} {d.color or 'N/A'}/{d.clarity or 'N/A'} (Cert: {d.certificate_no or 'N/A'})",
                            'date': d.created_at.isoformat()
                        })
            except Exception:
                pass

            # Get recent diamond status changes (if we had an audit log)
            try:
                # Get diamonds that were recently updated (status changes)
                recent_diamonds = Diamond.query.filter(
                    Diamond.updated_at > Diamond.created_at
                ).order_by(Diamond.updated_at.desc()).limit(3).all()

                for d in recent_diamonds:
                    if d.updated_at:
                        status_desc = f"Diamond status updated: {d.carat or 0}ct {d.shape.name if d.shape else 'Unknown'} - Status: {d.status or 'Unknown'}"
                        all_activity.append({
                            'type': 'diamond_update',
                            'description': status_desc,
                            'date': d.updated_at.isoformat()
                        })
            except Exception:
                pass

            # Sort by date (descending)
            all_activity.sort(key=lambda x: x['date'], reverse=True)
            return all_activity[:10], 200
        except Exception as e:
            dashboard_ns.abort(500, f'Failed to get recent activity: {str(e)}')

@dashboard_ns.route('/sales')
class DashboardSalesStats(Resource):
    @dashboard_ns.doc('dashboard_sales_stats')
    @dashboard_ns.marshal_with(sales_stats_model)
    @dashboard_ns.response(200, 'Sales statistics', sales_stats_model)
    @dashboard_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """Get sales statistics"""
        # DEBUG LOGGING
        unpaid_sales = Sale.query.filter(Sale.payment_status == 'unpaid').count()
        paid_sales = Sale.query.filter(Sale.payment_status == 'paid').count()
        total_sales = Sale.query.count()
        print(f"[DASHBOARD SALES] total_sales={total_sales}, paid_sales={paid_sales}, unpaid_sales={unpaid_sales}")
        return {'total_sales': total_sales, 'paid_sales': paid_sales, 'unpaid_sales': unpaid_sales}

@dashboard_ns.route('/stock')
class DashboardStockStats(Resource):
    @dashboard_ns.doc('dashboard_stock_stats')
    @dashboard_ns.marshal_with(stock_stats_model)
    @dashboard_ns.response(200, 'Stock statistics', stock_stats_model)
    @dashboard_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """Get stock statistics"""
        # Fix diamond counting - use SUM of available quantities instead of COUNT
        diamonds_in_stock = db.session.query(
            func.coalesce(func.sum(Diamond.available_quantity), 0)
        ).filter(
            Diamond.status.in_(['in_stock', 'reserved'])
        ).scalar() or 0

        jewelry_in_stock = JewelryItem.query.filter_by(status='in_stock').count()

        return {
            'diamonds_in_stock': int(diamonds_in_stock),
            'jewelry_in_stock': jewelry_in_stock
        }

@dashboard_ns.route('/analytics')
class DashboardAnalytics(Resource):
    @dashboard_ns.doc('get_dashboard_analytics')
    @dashboard_ns.response(200, 'Analytics data retrieved successfully')
    @dashboard_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """Get comprehensive analytics data for the dashboard."""
        try:
            from datetime import datetime, timedelta
            from app.models.diamond import Shape
            from app.models.vendor import Vendor

            # Date ranges
            today = datetime.now().date()
            month_ago = today - timedelta(days=30)

            # Enhanced Diamond analytics
            total_diamonds = Diamond.query.count()
            total_value = db.session.query(func.sum(Diamond.retail_price)).filter(
                Diamond.retail_price.isnot(None)
            ).scalar() or 0

            # Carat weight analytics
            total_carat_weight = db.session.query(
                func.coalesce(func.sum(Diamond.carat * Diamond.quantity), 0)
            ).scalar() or 0.0

            average_carat = db.session.query(func.avg(Diamond.carat)).scalar() or 0.0
            max_carat = db.session.query(func.max(Diamond.carat)).scalar() or 0.0
            min_carat = db.session.query(func.min(Diamond.carat)).scalar() or 0.0

            # Value analytics
            average_cost_price = db.session.query(func.avg(Diamond.cost_price)).filter(
                Diamond.cost_price.isnot(None)
            ).scalar() or 0.0

            average_retail_price = db.session.query(func.avg(Diamond.retail_price)).filter(
                Diamond.retail_price.isnot(None)
            ).scalar() or 0.0

            avg_price = db.session.query(func.avg(Diamond.retail_price)).filter(
                Diamond.retail_price.isnot(None)
            ).scalar() or 0

            # Low stock diamonds
            low_stock_count = Diamond.query.filter(
                Diamond.quantity <= Diamond.minimum_stock
            ).count()

            # Status distribution
            status_distribution = db.session.query(
                Diamond.status,
                func.count(Diamond.id).label('count')
            ).group_by(Diamond.status).all()

            # Shape distribution
            shape_distribution = db.session.query(
                Shape.name,
                func.count(Diamond.id).label('count')
            ).join(Diamond).group_by(Shape.name).all()

            # Color distribution
            color_distribution = db.session.query(
                Diamond.color,
                func.count(Diamond.id).label('count')
            ).group_by(Diamond.color).all()

            # Recent additions (last 30 days)
            recent_additions = Diamond.query.filter(
                Diamond.created_at >= month_ago
            ).count()

            return {
                'overview': {
                    'total_diamonds': total_diamonds,
                    'total_value': float(total_value),
                    'average_price': float(avg_price),
                    'low_stock_count': low_stock_count,
                    'recent_additions': recent_additions
                },
                'carat_analytics': {
                    'total_carat_weight': round(float(total_carat_weight), 2),
                    'average_carat': round(float(average_carat), 3),
                    'max_carat': round(float(max_carat), 2),
                    'min_carat': round(float(min_carat), 2)
                },
                'value_analytics': {
                    'average_cost_price': round(float(average_cost_price), 2),
                    'average_retail_price': round(float(average_retail_price), 2),
                    'profit_margin': round(((average_retail_price - average_cost_price) / average_cost_price * 100), 2) if average_cost_price > 0 else 0
                },
                'distributions': {
                    'status': [{'status': s[0], 'count': s[1]} for s in status_distribution],
                    'shape': [{'shape': s[0], 'count': s[1]} for s in shape_distribution],
                    'color': [{'color': s[0], 'count': s[1]} for s in color_distribution]
                }
            }, 200

        except Exception as e:
            import logging
            logging.error(f"Dashboard analytics failed: {str(e)}")
            dashboard_ns.abort(500, f'Failed to fetch analytics: {str(e)}')

# Diamond Alerts Model
alert_model = dashboard_ns.model('DiamondAlert', {
    'type': fields.String,
    'severity': fields.String,
    'message': fields.String,
    'diamond_id': fields.Integer,
    'diamond_info': fields.String
})

@dashboard_ns.route('/alerts')
class DashboardAlerts(Resource):
    @dashboard_ns.doc('get_diamond_alerts')
    @dashboard_ns.marshal_list_with(alert_model)
    @dashboard_ns.response(200, 'Diamond alerts retrieved successfully')
    @dashboard_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """Get diamond stock alerts and critical notifications."""
        try:
            print("DEBUG: Alerts endpoint called")  # Debug logging
            alerts = []

            # Low stock alerts
            low_stock_diamonds = Diamond.query.filter(
                Diamond.available_quantity <= Diamond.minimum_stock,
                Diamond.status.in_(['in_stock', 'reserved'])
            ).all()

            for diamond in low_stock_diamonds:
                alerts.append({
                    'type': 'low_stock',
                    'severity': 'warning' if diamond.available_quantity > 0 else 'critical',
                    'message': f"Low stock: {diamond.available_quantity} available (min: {diamond.minimum_stock})",
                    'diamond_id': diamond.id,
                    'diamond_info': f"{diamond.carat}ct {diamond.shape.name if diamond.shape else 'Unknown'} {diamond.color}/{diamond.clarity}"
                })

            # High value diamonds with low stock
            high_value_low_stock = Diamond.query.filter(
                Diamond.retail_price > 100000,  # High value threshold
                Diamond.available_quantity <= 2,
                Diamond.status.in_(['in_stock', 'reserved'])
            ).all()

            for diamond in high_value_low_stock:
                alerts.append({
                    'type': 'high_value_low_stock',
                    'severity': 'critical',
                    'message': f"High-value diamond low stock: ₹{diamond.retail_price:,.0f} - {diamond.available_quantity} available",
                    'diamond_id': diamond.id,
                    'diamond_info': f"{diamond.carat}ct {diamond.shape.name if diamond.shape else 'Unknown'} {diamond.color}/{diamond.clarity}"
                })

            # Diamonds without certificates
            no_certificate = Diamond.query.filter(
                Diamond.certificate_no.is_(None),
                Diamond.status.in_(['in_stock', 'reserved'])
            ).count()

            if no_certificate > 0:
                alerts.append({
                    'type': 'missing_certificate',
                    'severity': 'warning',
                    'message': f"{no_certificate} diamonds missing certificates",
                    'diamond_id': None,
                    'diamond_info': 'Multiple diamonds affected'
                })

            # Reserved diamonds for too long (over 30 days)
            from datetime import datetime, timedelta
            thirty_days_ago = datetime.now() - timedelta(days=30)

            long_reserved = Diamond.query.filter(
                Diamond.status == 'reserved',
                Diamond.updated_at < thirty_days_ago
            ).count()

            if long_reserved > 0:
                alerts.append({
                    'type': 'long_reserved',
                    'severity': 'warning',
                    'message': f"{long_reserved} diamonds reserved for over 30 days",
                    'diamond_id': None,
                    'diamond_info': 'Review reservation status'
                })

            return alerts, 200

        except Exception as e:
            import logging
            logging.error(f"Dashboard alerts failed: {str(e)}")
            dashboard_ns.abort(500, f'Failed to fetch alerts: {str(e)}')
