#!/usr/bin/env python3
"""
Cross-Page Data Consistency Validation
Validates that all pages display consistent and accurate data
"""

import sys
import os
import requests
import json
from datetime import datetime

class CrossPageValidator:
    def __init__(self, base_url='http://localhost:8000/api'):
        self.base_url = base_url
        self.session = requests.Session()
        self.validation_results = []

    def authenticate(self):
        """Authenticate with the API"""
        try:
            auth_data = {
                "username": "admin",
                "password": "admin123"
            }

            response = self.session.post(f"{self.base_url}/auth/login", json=auth_data)
            if response.status_code == 200:
                data = response.json()
                if 'access_token' in data:
                    self.session.headers.update({
                        'Authorization': f"Bearer {data['access_token']}"
                    })
                    return True
            return False
        except Exception:
            return False
        
    def log_validation(self, test_name, success, message="", data=None):
        """Log validation results"""
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'data': data
        }
        self.validation_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
    def get_dashboard_data(self):
        """Get dashboard data"""
        try:
            response = self.session.get(f"{self.base_url}/dashboard/summary")
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            print(f"Error getting dashboard data: {e}")
            return None
    
    def get_diamonds_data(self):
        """Get diamonds data"""
        try:
            response = self.session.get(f"{self.base_url}/diamonds")
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            print(f"Error getting diamonds data: {e}")
            return None
    
    def get_jewelry_data(self):
        """Get jewelry data"""
        try:
            response = self.session.get(f"{self.base_url}/jewelry")
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            print(f"Error getting jewelry data: {e}")
            return None
    
    def get_sales_data(self):
        """Get sales data"""
        try:
            response = self.session.get(f"{self.base_url}/sales")
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            print(f"Error getting sales data: {e}")
            return None
    
    def validate_diamond_counts(self):
        """Validate diamond counts across dashboard and diamond list"""
        dashboard_data = self.get_dashboard_data()
        diamonds_data = self.get_diamonds_data()
        
        if not dashboard_data or not diamonds_data:
            self.log_validation("Diamond Count Consistency", False, "Could not fetch required data")
            return False
        
        # Calculate actual diamond counts from diamond list
        total_quantity = 0
        available_quantity = 0
        reserved_quantity = 0
        sold_quantity = 0
        manufacturing_quantity = 0
        
        for diamond in diamonds_data:
            qty = diamond.get('quantity', 0) or 0
            available_qty = diamond.get('available_quantity', 0) or 0
            reserved_qty = diamond.get('reserved_quantity', 0) or 0
            status = diamond.get('status', '')
            
            total_quantity += qty
            available_quantity += available_qty
            reserved_quantity += reserved_qty
            
            if status == 'sold':
                sold_quantity += qty
            elif status == 'manufacturing':
                manufacturing_quantity += qty
        
        # Compare with dashboard data
        dashboard_total = dashboard_data.get('total_diamonds', 0)
        dashboard_available = dashboard_data.get('diamonds_in_stock', 0)
        dashboard_reserved = dashboard_data.get('diamonds_reserved', 0)
        dashboard_sold = dashboard_data.get('diamonds_sold', 0)
        dashboard_manufacturing = dashboard_data.get('diamonds_manufacturing', 0)
        
        inconsistencies = []
        
        if total_quantity != dashboard_total:
            inconsistencies.append(f"Total: {total_quantity} vs {dashboard_total}")
        
        if available_quantity != dashboard_available:
            inconsistencies.append(f"Available: {available_quantity} vs {dashboard_available}")
        
        if reserved_quantity != dashboard_reserved:
            inconsistencies.append(f"Reserved: {reserved_quantity} vs {dashboard_reserved}")
        
        if sold_quantity != dashboard_sold:
            inconsistencies.append(f"Sold: {sold_quantity} vs {dashboard_sold}")
        
        if manufacturing_quantity != dashboard_manufacturing:
            inconsistencies.append(f"Manufacturing: {manufacturing_quantity} vs {dashboard_manufacturing}")
        
        if inconsistencies:
            self.log_validation("Diamond Count Consistency", False, 
                              f"Inconsistencies found: {', '.join(inconsistencies)}")
            return False
        else:
            self.log_validation("Diamond Count Consistency", True, 
                              f"All counts match: Total={total_quantity}, Available={available_quantity}")
            return True
    
    def validate_jewelry_diamond_associations(self):
        """Validate jewelry-diamond associations"""
        jewelry_data = self.get_jewelry_data()
        diamonds_data = self.get_diamonds_data()
        
        if not jewelry_data or not diamonds_data:
            self.log_validation("Jewelry-Diamond Associations", False, "Could not fetch required data")
            return False
        
        # Create diamond lookup
        diamond_lookup = {d['id']: d for d in diamonds_data}
        
        issues = []
        total_reserved_from_jewelry = 0
        
        for jewelry in jewelry_data:
            if jewelry.get('diamonds'):
                for diamond_assoc in jewelry['diamonds']:
                    diamond_id = diamond_assoc.get('diamond_id')
                    quantity_used = diamond_assoc.get('quantity', 0)
                    
                    if diamond_id in diamond_lookup:
                        diamond = diamond_lookup[diamond_id]
                        total_reserved_from_jewelry += quantity_used
                        
                        # Check if diamond has enough quantity
                        if diamond.get('quantity', 0) < quantity_used:
                            issues.append(f"Jewelry {jewelry['id']} uses {quantity_used} of diamond {diamond_id} but only {diamond.get('quantity', 0)} available")
                    else:
                        issues.append(f"Jewelry {jewelry['id']} references non-existent diamond {diamond_id}")
        
        if issues:
            self.log_validation("Jewelry-Diamond Associations", False, 
                              f"Issues found: {'; '.join(issues[:3])}{'...' if len(issues) > 3 else ''}")
            return False
        else:
            self.log_validation("Jewelry-Diamond Associations", True, 
                              f"All associations valid. Total reserved: {total_reserved_from_jewelry}")
            return True
    
    def validate_sales_jewelry_tracking(self):
        """Validate sales-jewelry tracking"""
        sales_data = self.get_sales_data()
        jewelry_data = self.get_jewelry_data()
        
        if not sales_data or not jewelry_data:
            self.log_validation("Sales-Jewelry Tracking", False, "Could not fetch required data")
            return False
        
        # Create jewelry lookup
        jewelry_lookup = {j['id']: j for j in jewelry_data}
        
        issues = []
        sold_jewelry_ids = set()
        
        for sale in sales_data:
            jewelry_id = sale.get('jewelry_id')
            if jewelry_id:
                sold_jewelry_ids.add(jewelry_id)
                
                if jewelry_id not in jewelry_lookup:
                    issues.append(f"Sale {sale['id']} references non-existent jewelry {jewelry_id}")
                else:
                    jewelry = jewelry_lookup[jewelry_id]
                    if jewelry.get('status') != 'sold':
                        issues.append(f"Sale {sale['id']} references jewelry {jewelry_id} but jewelry status is {jewelry.get('status')}")
        
        # Check if sold jewelry items have corresponding sales
        for jewelry in jewelry_data:
            if jewelry.get('status') == 'sold' and jewelry['id'] not in sold_jewelry_ids:
                issues.append(f"Jewelry {jewelry['id']} is marked as sold but has no corresponding sale record")
        
        if issues:
            self.log_validation("Sales-Jewelry Tracking", False, 
                              f"Issues found: {'; '.join(issues[:3])}{'...' if len(issues) > 3 else ''}")
            return False
        else:
            self.log_validation("Sales-Jewelry Tracking", True, 
                              f"All sales-jewelry tracking is consistent")
            return True
    
    def validate_data_types_and_nulls(self):
        """Validate data types and null values"""
        dashboard_data = self.get_dashboard_data()
        diamonds_data = self.get_diamonds_data()
        
        if not dashboard_data or not diamonds_data:
            self.log_validation("Data Types and Nulls", False, "Could not fetch required data")
            return False
        
        issues = []
        
        # Check dashboard data types
        required_dashboard_fields = [
            'diamonds_in_stock', 'total_diamonds', 'diamonds_sold',
            'diamonds_manufacturing', 'diamonds_reserved'
        ]
        
        for field in required_dashboard_fields:
            value = dashboard_data.get(field)
            if not isinstance(value, int) or value < 0:
                issues.append(f"Dashboard {field} has invalid value: {value}")
        
        # Check diamond data
        for i, diamond in enumerate(diamonds_data[:10]):  # Check first 10 diamonds
            required_fields = ['id', 'carat', 'color', 'clarity', 'certificate_no']
            for field in required_fields:
                if field not in diamond or diamond[field] is None:
                    issues.append(f"Diamond {i} missing required field: {field}")
            
            # Check quantity fields
            quantity_fields = ['quantity', 'available_quantity', 'reserved_quantity']
            for field in quantity_fields:
                value = diamond.get(field)
                if value is not None and (not isinstance(value, int) or value < 0):
                    issues.append(f"Diamond {i} has invalid {field}: {value}")
        
        if issues:
            self.log_validation("Data Types and Nulls", False, 
                              f"Issues found: {'; '.join(issues[:3])}{'...' if len(issues) > 3 else ''}")
            return False
        else:
            self.log_validation("Data Types and Nulls", True, "All data types and values are valid")
            return True
    
    def run_all_validations(self):
        """Run all cross-page validations"""
        print("🔍 CROSS-PAGE DATA CONSISTENCY VALIDATION")
        print("=" * 50)

        # Try to authenticate first
        auth_success = self.authenticate()
        if not auth_success:
            print("⚠️  Authentication failed, proceeding with tests (some may fail)")

        validations = [
            self.validate_diamond_counts,
            self.validate_jewelry_diamond_associations,
            self.validate_sales_jewelry_tracking,
            self.validate_data_types_and_nulls
        ]
        
        for validation in validations:
            try:
                validation()
            except Exception as e:
                self.log_validation(validation.__name__, False, f"Validation failed: {str(e)}")
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 VALIDATION SUMMARY")
        print("=" * 50)
        
        total_validations = len(self.validation_results)
        passed_validations = sum(1 for result in self.validation_results if result['success'])
        failed_validations = total_validations - passed_validations
        
        print(f"Total Validations: {total_validations}")
        print(f"Passed: {passed_validations}")
        print(f"Failed: {failed_validations}")
        print(f"Success Rate: {(passed_validations/total_validations)*100:.1f}%")
        
        if failed_validations > 0:
            print("\n❌ FAILED VALIDATIONS:")
            for result in self.validation_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['message']}")
        
        return failed_validations == 0

if __name__ == "__main__":
    validator = CrossPageValidator()
    success = validator.run_all_validations()
    sys.exit(0 if success else 1)
