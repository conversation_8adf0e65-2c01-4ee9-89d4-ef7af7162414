#!/usr/bin/env python3
"""
Diamond Data Fix Script
Fixes common diamond data issues that cause zero carat weight
"""

import sys
import os
import requests
import json

def fix_diamond_data():
    """Fix diamond data issues"""
    base_url = 'http://localhost:8000/api'
    
    print("🔧 DIAMOND DATA FIX SCRIPT")
    print("=" * 50)
    
    # Authenticate
    try:
        auth_data = {"username": "admin", "password": "admin123"}
        response = requests.post(f"{base_url}/auth/login", json=auth_data, timeout=5)
        
        if response.status_code != 200:
            print("❌ Authentication failed")
            return False
        
        data = response.json()
        token = data.get('access_token')
        headers = {'Authorization': f'Bearer {token}'}
        print("✅ Authentication successful")
        
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False
    
    # Get current diamond data
    try:
        response = requests.get(f"{base_url}/diamonds", headers=headers, timeout=10)
        if response.status_code != 200:
            print(f"❌ Failed to fetch diamonds: {response.status_code}")
            return False
        
        diamonds = response.json()
        print(f"📊 Found {len(diamonds)} diamonds")
        
        if len(diamonds) == 0:
            print("⚠️  No diamonds found. Creating sample diamonds...")
            return create_sample_diamonds(base_url, headers)
        
    except Exception as e:
        print(f"❌ Error fetching diamonds: {e}")
        return False
    
    # Analyze and fix issues
    fixes_applied = 0
    
    print("\n🔍 ANALYZING AND FIXING DIAMOND DATA")
    print("-" * 40)
    
    for diamond in diamonds:
        diamond_id = diamond.get('id')
        issues = []
        fixes = {}
        
        # Check carat value
        carat = diamond.get('carat')
        if carat is None or carat <= 0:
            issues.append("Missing/invalid carat")
            fixes['carat'] = 1.0  # Default 1 carat
        
        # Check quantity
        quantity = diamond.get('quantity')
        if quantity is None or quantity <= 0:
            issues.append("Missing/invalid quantity")
            fixes['quantity'] = 1  # Default 1 piece
        
        # Check available_quantity
        available_quantity = diamond.get('available_quantity')
        reserved_quantity = diamond.get('reserved_quantity', 0)
        expected_available = (quantity or 1) - (reserved_quantity or 0)
        
        if available_quantity is None or available_quantity != expected_available:
            issues.append("Incorrect available_quantity")
            fixes['available_quantity'] = max(0, expected_available)
        
        # Check status
        status = diamond.get('status')
        if not status:
            issues.append("Missing status")
            fixes['status'] = 'in_stock'
        
        # Apply fixes if needed
        if issues:
            print(f"\nDiamond {diamond_id}: {', '.join(issues)}")
            
            try:
                response = requests.put(
                    f"{base_url}/diamonds/{diamond_id}",
                    json=fixes,
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    print(f"  ✅ Fixed: {fixes}")
                    fixes_applied += 1
                else:
                    print(f"  ❌ Fix failed: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Fix error: {e}")
    
    print(f"\n📊 FIXES SUMMARY")
    print("-" * 20)
    print(f"Diamonds processed: {len(diamonds)}")
    print(f"Fixes applied: {fixes_applied}")
    
    return fixes_applied > 0

def create_sample_diamonds(base_url, headers):
    """Create sample diamonds for testing"""
    print("\n🔨 CREATING SAMPLE DIAMONDS")
    print("-" * 30)
    
    sample_diamonds = [
        {
            "shape_id": 1,
            "carat": 1.25,
            "color": "D",
            "clarity": "VVS1",
            "certificate_no": "SAMPLE001",
            "quantity": 2,
            "cost_price": 50000,
            "retail_price": 75000,
            "status": "in_stock"
        },
        {
            "shape_id": 1,
            "carat": 0.75,
            "color": "E",
            "clarity": "VS1",
            "certificate_no": "SAMPLE002",
            "quantity": 3,
            "cost_price": 30000,
            "retail_price": 45000,
            "status": "in_stock"
        },
        {
            "shape_id": 1,
            "carat": 2.0,
            "color": "F",
            "clarity": "VVS2",
            "certificate_no": "SAMPLE003",
            "quantity": 1,
            "cost_price": 80000,
            "retail_price": 120000,
            "status": "in_stock"
        },
        {
            "shape_id": 1,
            "carat": 1.5,
            "color": "G",
            "clarity": "VS2",
            "certificate_no": "SAMPLE004",
            "quantity": 2,
            "cost_price": 60000,
            "retail_price": 90000,
            "status": "reserved",
            "reserved_quantity": 1
        },
        {
            "shape_id": 1,
            "carat": 0.5,
            "color": "H",
            "clarity": "SI1",
            "certificate_no": "SAMPLE005",
            "quantity": 5,
            "cost_price": 15000,
            "retail_price": 25000,
            "status": "in_stock"
        }
    ]
    
    created_count = 0
    
    for i, diamond_data in enumerate(sample_diamonds):
        try:
            response = requests.post(
                f"{base_url}/diamonds",
                json=diamond_data,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 201:
                print(f"✅ Created sample diamond {i+1}: {diamond_data['carat']}ct {diamond_data['color']}/{diamond_data['clarity']}")
                created_count += 1
            else:
                print(f"❌ Failed to create diamond {i+1}: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error creating diamond {i+1}: {e}")
    
    print(f"\n📊 Created {created_count}/{len(sample_diamonds)} sample diamonds")
    return created_count > 0

def test_dashboard_after_fix(base_url, headers):
    """Test dashboard data after fixes"""
    print("\n🧪 TESTING DASHBOARD AFTER FIXES")
    print("-" * 35)
    
    try:
        response = requests.get(f"{base_url}/dashboard/summary", headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            print("📊 Dashboard Results:")
            print(f"  Total diamonds: {data.get('total_diamonds', 'N/A')}")
            print(f"  Diamonds in stock: {data.get('diamonds_in_stock', 'N/A')}")
            print(f"  Total carat weight: {data.get('total_carat_weight', 'N/A')}ct")
            print(f"  Available carat weight: {data.get('available_carat_weight', 'N/A')}ct")
            print(f"  Reserved carat weight: {data.get('reserved_carat_weight', 'N/A')}ct")
            print(f"  Average carat per diamond: {data.get('average_carat_per_diamond', 'N/A')}ct")
            
            # Check if fixes worked
            total_carat = data.get('total_carat_weight', 0)
            if total_carat > 0:
                print("\n✅ SUCCESS: Carat weight is now showing correctly!")
                return True
            else:
                print("\n⚠️  Carat weight is still 0. May need backend restart.")
                return False
        else:
            print(f"❌ Dashboard request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing dashboard: {e}")
        return False

if __name__ == "__main__":
    print("🧪 DIAMOND DATA FIX TOOL")
    print("=" * 50)
    
    # Authenticate and get base URL
    base_url = 'http://localhost:8000/api'
    
    try:
        auth_data = {"username": "admin", "password": "admin123"}
        response = requests.post(f"{base_url}/auth/login", json=auth_data, timeout=5)
        
        if response.status_code != 200:
            print("❌ Cannot connect to backend or authentication failed")
            print("💡 Make sure backend is running: python run.py")
            sys.exit(1)
        
        data = response.json()
        token = data.get('access_token')
        headers = {'Authorization': f'Bearer {token}'}
        
        # Run fixes
        success = fix_diamond_data()
        
        if success:
            # Test dashboard after fixes
            dashboard_ok = test_dashboard_after_fix(base_url, headers)
            
            if dashboard_ok:
                print("\n🎉 ALL FIXES SUCCESSFUL!")
                print("✅ Diamond data is now correct")
                print("✅ Dashboard should show proper carat weights")
            else:
                print("\n⚠️  FIXES APPLIED BUT DASHBOARD STILL SHOWS ISSUES")
                print("💡 Try restarting the backend server")
        else:
            print("\n⚠️  NO FIXES WERE NEEDED OR APPLIED")
            print("💡 Run diagnose_diamond_data.py for detailed analysis")
        
        print("\n📝 NEXT STEPS:")
        print("1. Refresh the dashboard page")
        print("2. Check if carat weights now show correctly")
        print("3. If still showing 0, restart backend server")
        print("4. Run comprehensive tests again")
        
    except Exception as e:
        print(f"❌ Script failed: {e}")
        sys.exit(1)
