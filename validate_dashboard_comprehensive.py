#!/usr/bin/env python3
"""
Comprehensive Dashboard Validation
Tests all dashboard sections and functionality for production readiness
"""

import sys
import os
import requests
import json
from datetime import datetime

class DashboardValidator:
    def __init__(self, base_url='http://localhost:8000/api'):
        self.base_url = base_url
        self.session = requests.Session()
        self.validation_results = []
        
    def log_validation(self, test_name, success, message="", data=None):
        """Log validation results"""
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'data': data
        }
        self.validation_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
    def validate_dashboard_summary(self):
        """Validate dashboard summary endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/dashboard/summary")
            if response.status_code != 200:
                self.log_validation("Dashboard Summary", False, f"HTTP {response.status_code}")
                return False
            
            data = response.json()
            
            # Check for new carat weight fields
            required_fields = [
                'diamonds_in_stock', 'total_diamonds', 'diamonds_sold',
                'diamonds_manufacturing', 'diamonds_reserved',
                'total_carat_weight', 'available_carat_weight', 
                'reserved_carat_weight', 'average_carat_per_diamond'
            ]
            
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                self.log_validation("Dashboard Summary", False, 
                                  f"Missing fields: {missing_fields}")
                return False
            
            # Validate carat weight values
            carat_fields = ['total_carat_weight', 'available_carat_weight', 'reserved_carat_weight']
            for field in carat_fields:
                if not isinstance(data[field], (int, float)) or data[field] < 0:
                    self.log_validation("Dashboard Summary", False, 
                                      f"Invalid {field}: {data[field]}")
                    return False
            
            self.log_validation("Dashboard Summary", True, 
                              f"All fields valid. Total carat: {data['total_carat_weight']}ct")
            return True
            
        except Exception as e:
            self.log_validation("Dashboard Summary", False, f"Exception: {str(e)}")
            return False
    
    def validate_recent_activity(self):
        """Validate recent activity endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/dashboard/activity")
            if response.status_code != 200:
                self.log_validation("Recent Activity", False, f"HTTP {response.status_code}")
                return False
            
            data = response.json()
            if not isinstance(data, list):
                self.log_validation("Recent Activity", False, "Response is not a list")
                return False
            
            # Check for diamond activities
            diamond_activities = [item for item in data if item.get('type') in ['diamond', 'diamond_update']]
            
            # Validate activity structure
            for activity in data[:5]:  # Check first 5 activities
                required_fields = ['type', 'description', 'date']
                missing_fields = [field for field in required_fields if field not in activity]
                if missing_fields:
                    self.log_validation("Recent Activity", False, 
                                      f"Activity missing fields: {missing_fields}")
                    return False
            
            self.log_validation("Recent Activity", True, 
                              f"Activity feed working. {len(diamond_activities)} diamond activities found")
            return True
            
        except Exception as e:
            self.log_validation("Recent Activity", False, f"Exception: {str(e)}")
            return False
    
    def validate_analytics(self):
        """Validate analytics endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/dashboard/analytics")
            if response.status_code != 200:
                self.log_validation("Analytics", False, f"HTTP {response.status_code}")
                return False
            
            data = response.json()
            
            # Check for new analytics sections
            required_sections = ['overview', 'carat_analytics', 'value_analytics', 'distributions']
            missing_sections = [section for section in required_sections if section not in data]
            if missing_sections:
                self.log_validation("Analytics", False, 
                                  f"Missing sections: {missing_sections}")
                return False
            
            # Validate carat analytics
            carat_analytics = data['carat_analytics']
            required_carat_fields = ['total_carat_weight', 'average_carat', 'max_carat', 'min_carat']
            missing_carat_fields = [field for field in required_carat_fields if field not in carat_analytics]
            if missing_carat_fields:
                self.log_validation("Analytics", False, 
                                  f"Missing carat analytics: {missing_carat_fields}")
                return False
            
            # Validate value analytics
            value_analytics = data['value_analytics']
            required_value_fields = ['average_cost_price', 'average_retail_price', 'profit_margin']
            missing_value_fields = [field for field in required_value_fields if field not in value_analytics]
            if missing_value_fields:
                self.log_validation("Analytics", False, 
                                  f"Missing value analytics: {missing_value_fields}")
                return False
            
            self.log_validation("Analytics", True, 
                              f"Analytics complete. Total carat: {carat_analytics['total_carat_weight']}ct")
            return True
            
        except Exception as e:
            self.log_validation("Analytics", False, f"Exception: {str(e)}")
            return False
    
    def validate_alerts(self):
        """Validate alerts endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/dashboard/alerts")
            if response.status_code != 200:
                self.log_validation("Alerts", False, f"HTTP {response.status_code}")
                return False
            
            data = response.json()
            if not isinstance(data, list):
                self.log_validation("Alerts", False, "Response is not a list")
                return False
            
            # Validate alert structure
            for alert in data[:3]:  # Check first 3 alerts
                required_fields = ['type', 'severity', 'message']
                missing_fields = [field for field in required_fields if field not in alert]
                if missing_fields:
                    self.log_validation("Alerts", False, 
                                      f"Alert missing fields: {missing_fields}")
                    return False
                
                if alert['severity'] not in ['warning', 'critical']:
                    self.log_validation("Alerts", False, 
                                      f"Invalid severity: {alert['severity']}")
                    return False
            
            critical_alerts = [alert for alert in data if alert.get('severity') == 'critical']
            
            self.log_validation("Alerts", True, 
                              f"Alerts working. {len(data)} total, {len(critical_alerts)} critical")
            return True
            
        except Exception as e:
            self.log_validation("Alerts", False, f"Exception: {str(e)}")
            return False
    
    def validate_sales_stats(self):
        """Validate sales statistics"""
        try:
            response = self.session.get(f"{self.base_url}/dashboard/sales")
            if response.status_code != 200:
                self.log_validation("Sales Stats", False, f"HTTP {response.status_code}")
                return False
            
            data = response.json()
            required_fields = ['total_sales', 'paid_sales', 'unpaid_sales']
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                self.log_validation("Sales Stats", False, 
                                  f"Missing fields: {missing_fields}")
                return False
            
            # Validate logical consistency
            total = data['total_sales']
            paid = data['paid_sales']
            unpaid = data['unpaid_sales']
            
            if paid + unpaid != total:
                self.log_validation("Sales Stats", False, 
                                  f"Inconsistent totals: {paid} + {unpaid} != {total}")
                return False
            
            self.log_validation("Sales Stats", True, 
                              f"Sales stats consistent. Total: {total}")
            return True
            
        except Exception as e:
            self.log_validation("Sales Stats", False, f"Exception: {str(e)}")
            return False
    
    def validate_stock_levels(self):
        """Validate stock levels"""
        try:
            response = self.session.get(f"{self.base_url}/dashboard/stock")
            if response.status_code != 200:
                self.log_validation("Stock Levels", False, f"HTTP {response.status_code}")
                return False
            
            data = response.json()
            required_fields = ['diamonds_in_stock', 'jewelry_in_stock']
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                self.log_validation("Stock Levels", False, 
                                  f"Missing fields: {missing_fields}")
                return False
            
            # Validate non-negative values
            for field in required_fields:
                if not isinstance(data[field], int) or data[field] < 0:
                    self.log_validation("Stock Levels", False, 
                                      f"Invalid {field}: {data[field]}")
                    return False
            
            self.log_validation("Stock Levels", True, 
                              f"Stock levels valid. Diamonds: {data['diamonds_in_stock']}")
            return True
            
        except Exception as e:
            self.log_validation("Stock Levels", False, f"Exception: {str(e)}")
            return False
    
    def run_all_validations(self):
        """Run all dashboard validations"""
        print("🔍 COMPREHENSIVE DASHBOARD VALIDATION")
        print("=" * 50)
        
        validations = [
            self.validate_dashboard_summary,
            self.validate_recent_activity,
            self.validate_analytics,
            self.validate_alerts,
            self.validate_sales_stats,
            self.validate_stock_levels
        ]
        
        for validation in validations:
            try:
                validation()
            except Exception as e:
                self.log_validation(validation.__name__, False, f"Validation failed: {str(e)}")
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 VALIDATION SUMMARY")
        print("=" * 50)
        
        total_validations = len(self.validation_results)
        passed_validations = sum(1 for result in self.validation_results if result['success'])
        failed_validations = total_validations - passed_validations
        
        print(f"Total Validations: {total_validations}")
        print(f"Passed: {passed_validations}")
        print(f"Failed: {failed_validations}")
        print(f"Success Rate: {(passed_validations/total_validations)*100:.1f}%")
        
        if failed_validations > 0:
            print("\n❌ FAILED VALIDATIONS:")
            for result in self.validation_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['message']}")
        else:
            print("\n🎉 ALL DASHBOARD SECTIONS WORKING PERFECTLY!")
            print("✅ Recent Activity with diamond tracking")
            print("✅ Total carat weight display (TOP PRIORITY)")
            print("✅ Enhanced analytics with value tracking")
            print("✅ Diamond stock alerts system")
            print("✅ Comprehensive data validation")
        
        return failed_validations == 0

if __name__ == "__main__":
    validator = DashboardValidator()
    success = validator.run_all_validations()
    sys.exit(0 if success else 1)
