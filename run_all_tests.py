#!/usr/bin/env python3
"""
Comprehensive Test Runner
Runs all diamond system tests in the correct order
"""

import subprocess
import sys
import time
import requests

def check_backend():
    """Check if backend is running"""
    try:
        response = requests.get('http://localhost:8000', timeout=5)
        return True
    except:
        return False

def check_frontend():
    """Check if frontend is running"""
    try:
        response = requests.get('http://localhost:5173', timeout=5)
        return True
    except:
        return False

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n🔍 {description}")
    print("-" * 50)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        # Print output
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(result.stderr)
        
        success = result.returncode == 0
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"\n{status} {description}")
        
        return success
    except Exception as e:
        print(f"❌ Error running {description}: {e}")
        return False

def main():
    print("🧪 COMPREHENSIVE DIAMOND SYSTEM TEST SUITE")
    print("=" * 60)
    
    # Check prerequisites
    print("\n🔍 CHECKING PREREQUISITES")
    print("-" * 30)
    
    backend_running = check_backend()
    frontend_running = check_frontend()
    
    if backend_running:
        print("✅ Backend server is running")
    else:
        print("❌ Backend server is not running")
        print("💡 Start with: python run.py")
    
    if frontend_running:
        print("✅ Frontend server is running")
    else:
        print("❌ Frontend server is not running")
        print("💡 Start with: npm run dev")
    
    if not backend_running:
        print("\n⚠️  Backend is required for most tests")
        print("Starting basic connection test only...")
        
        # Run basic connection test
        run_command("python test_backend_connection.py", "Backend Connection Test")
        return
    
    # Test execution plan
    tests = [
        ("python test_backend_connection.py", "Backend Connection & Endpoint Test"),
        ("python test_diamond_system_comprehensive.py", "Diamond System Comprehensive Test"),
        ("python validate_cross_page_consistency.py", "Cross-Page Data Consistency Validation"),
        ("python validate_dashboard_comprehensive.py", "Dashboard Comprehensive Validation")
    ]
    
    # Frontend tests (if frontend is running)
    if frontend_running:
        tests.append(("node run_frontend_tests.js --simple", "Frontend Accessibility Test"))
    
    # Run all tests
    results = []
    
    for command, description in tests:
        success = run_command(command, description)
        results.append((description, success))
        
        # Small delay between tests
        time.sleep(1)
    
    # Print final summary
    print("\n" + "=" * 60)
    print("📊 FINAL TEST SUMMARY")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for _, success in results if success)
    failed_tests = total_tests - passed_tests
    
    print(f"Total Test Suites: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n📋 DETAILED RESULTS:")
    for description, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status} {description}")
    
    if failed_tests > 0:
        print("\n🔧 TROUBLESHOOTING TIPS:")
        print("1. Ensure both backend and frontend servers are running")
        print("2. Check admin credentials (admin/admin123)")
        print("3. Restart servers if endpoints show 404 errors")
        print("4. Check database connection and data")
        print("5. Review error messages above for specific issues")
    else:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Diamond system is working correctly")
        print("✅ All endpoints are accessible")
        print("✅ Data consistency is maintained")
        print("✅ Dashboard functionality is complete")
        if frontend_running:
            print("✅ Frontend pages are accessible")
    
    print("\n📝 NEXT STEPS:")
    if not frontend_running:
        print("• Start frontend server: npm run dev")
        print("• Run frontend tests: node run_frontend_tests.js")
    
    print("• Review any failed tests above")
    print("• Check application functionality manually")
    print("• Deploy to production when all tests pass")
    
    return failed_tests == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
