import React, { useState, useRef } from 'react';
import { Search, X } from 'lucide-react';
import Button from './Button';

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: (value: string) => void;
  onClear?: () => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  autoFocus?: boolean;
  showClearButton?: boolean;
  showSearchButton?: boolean;
}

const SearchInput: React.FC<SearchInputProps> = ({
  value,
  onChange,
  onSearch,
  onClear,
  placeholder = "Search...",
  className = "",
  disabled = false,
  autoFocus = false,
  showClearButton = true,
  showSearchButton = true
}) => {
  const [internalValue, setInternalValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInternalValue(newValue);
    onChange(newValue);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    }
  };

  const handleSearch = () => {
    onSearch(internalValue.trim());
  };

  const handleClear = () => {
    setInternalValue('');
    onChange('');
    if (onClear) {
      onClear();
    } else {
      onSearch(''); // Trigger search with empty value to show all results
    }
    inputRef.current?.focus();
  };

  // Update internal value when external value changes
  React.useEffect(() => {
    setInternalValue(value);
  }, [value]);

  return (
    <div className={`relative flex items-center ${className}`}>
      <div className="relative flex-1">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={internalValue}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          disabled={disabled}
          autoFocus={autoFocus}
          className={`
            block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
            disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
            ${internalValue ? 'pr-20' : 'pr-10'}
          `}
        />
        {showClearButton && internalValue && (
          <button
            type="button"
            onClick={handleClear}
            disabled={disabled}
            className="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 disabled:cursor-not-allowed"
            title="Clear search"
          >
            <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
          </button>
        )}
      </div>
      
      {showSearchButton && (
        <Button
          type="button"
          onClick={handleSearch}
          disabled={disabled}
          className="ml-2"
          size="sm"
        >
          <Search className="h-4 w-4 mr-1" />
          Search
        </Button>
      )}
    </div>
  );
};

export default SearchInput;
