import { useState, useCallback } from 'react';

interface UseSearchOptions {
  initialValue?: string;
  minLength?: number;
  onSearch?: (query: string) => void;
  onClear?: () => void;
}

interface UseSearchReturn {
  searchQuery: string;
  activeSearchQuery: string;
  isSearchActive: boolean;
  hasSearchResults: boolean;
  setSearchQuery: (query: string) => void;
  executeSearch: (query?: string) => void;
  clearSearch: () => void;
  resetSearch: () => void;
}

export const useSearch = (options: UseSearchOptions = {}): UseSearchReturn => {
  const {
    initialValue = '',
    minLength = 1,
    onSearch,
    onClear
  } = options;

  const [searchQuery, setSearchQuery] = useState(initialValue);
  const [activeSearchQuery, setActiveSearchQuery] = useState(initialValue);
  const [isSearchActive, setIsSearchActive] = useState(!!initialValue);

  const executeSearch = useCallback((query?: string) => {
    const searchTerm = query !== undefined ? query : searchQuery;
    const trimmedQuery = searchTerm.trim();
    
    // Only execute search if query meets minimum length or is empty (to show all results)
    if (trimmedQuery.length === 0 || trimmedQuery.length >= minLength) {
      setActiveSearchQuery(trimmedQuery);
      setIsSearchActive(trimmedQuery.length > 0);
      
      if (onSearch) {
        onSearch(trimmedQuery);
      }
    }
  }, [searchQuery, minLength, onSearch]);

  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setActiveSearchQuery('');
    setIsSearchActive(false);
    
    if (onClear) {
      onClear();
    } else if (onSearch) {
      onSearch('');
    }
  }, [onSearch, onClear]);

  const resetSearch = useCallback(() => {
    setSearchQuery(initialValue);
    setActiveSearchQuery(initialValue);
    setIsSearchActive(!!initialValue);
  }, [initialValue]);

  const hasSearchResults = isSearchActive && activeSearchQuery.length > 0;

  return {
    searchQuery,
    activeSearchQuery,
    isSearchActive,
    hasSearchResults,
    setSearchQuery,
    executeSearch,
    clearSearch,
    resetSearch
  };
};
